# VideoSR-Lite GUI 使用指南

## 🚀 快速开始

### 编译与启动
```bash
# 一键编译（包含命令行工具和GUI）
./build.sh

# 启动GUI应用
cd build && ./bin/VideoSRLiteGUI
```

## 🖥️ 界面功能

### 主要区域
1. **左侧控制面板**：文件管理、参数设置、处理控制
2. **右侧预览区域**：原图与增强后图像的对比显示

### 核心功能

#### 📁 文件管理
- **支持格式**：
  - 图片：jpg, jpeg, png, bmp, tiff, webp
  - 视频：mp4, avi, mov, mkv, wmv, flv, webm
- **添加文件**：点击"添加文件"按钮或拖拽文件到列表
- **实时预览**：选择文件后自动加载预览（无需点击加载预览按钮）
- **文件标识**：列表中显示文件类型标签 [图片] / [视频]

#### 🔍 预览功能
- **自动预览**：选中文件后300ms延迟自动加载预览
- **原图显示**：左侧显示原始图像或视频第一帧
- **增强预览**：右侧显示超分辨率处理后的结果
- **缩放控制**：支持25%、50%、75%、100%、150%、200%、300%缩放
- **尺寸信息**：显示原图和增强图的具体尺寸

#### ⚙️ 处理参数
- **超分辨率倍数**：×2倍（推荐）或×4倍
- **音频降噪**：启用/禁用（仅视频文件有效）
- **降噪强度**：0-100%可调
- **锐化强度**：0-100%可调

#### 🎯 处理控制
- **开始处理**：处理列表中的所有文件
- **暂停功能**：中途可暂停处理
- **进度显示**：实时显示处理进度
- **结果保存**：处理结果保存在原文件同目录，文件名添加"_SR"后缀

## 🛠️ 技术特性

### AI引擎
- **模型**：RealESRGAN x2plus fp16
- **自动路径识别**：智能查找模型文件位置
- **模型预热**：解决ONNX Runtime首次推理不稳定问题
- **内存优化**：大图像自动分块处理

### 界面优化
- **响应式布局**：1400×900默认窗口，最小1200×800
- **友好错误处理**：错误信息在状态栏显示，不弹窗打断
- **实时反馈**：文件添加统计、处理状态实时更新
- **工具提示**：鼠标悬停显示完整文件路径

## 📝 使用流程

### 图片处理
1. 点击"添加文件"选择图片
2. 在列表中选择图片（自动显示预览）
3. 调整缩放比例查看细节
4. 设置处理参数
5. 点击"开始处理"
6. 处理完成后查看结果（保存在原文件目录）

### 视频处理
1. 添加视频文件到列表
2. 选择视频预览第一帧效果
3. 启用音频降噪（可选）
4. 调整降噪和锐化强度
5. 开始处理（支持批量处理）

### 批量处理
1. 一次性添加多个图片/视频文件
2. 逐个预览确认效果
3. 统一设置处理参数
4. 批量开始处理

## 🔧 故障排除

### 模型初始化失败
- **现象**：启动时提示"超分辨率引擎初始化失败"
- **解决**：检查`onnx/RealESRGAN_x2plus.fp16.onnx`文件是否存在
- **路径**：支持多种相对路径自动识别

### 预览无法加载
- **图片问题**：检查文件格式是否支持
- **视频问题**：确认FFmpeg库正常安装
- **路径问题**：避免文件路径包含特殊字符

### 处理速度慢
- **建议**：选择×2倍而非×4倍超分
- **大图优化**：程序自动处理大尺寸图像
- **系统负载**：关闭其他占用GPU/CPU的程序

## 🚀 性能建议

- **首选×2倍超分**：速度快，效果好
- **图片建议尺寸**：单边不超过2000像素
- **批量处理**：建议每批不超过20个文件
- **内存监控**：处理大文件时注意系统内存使用

## 📋 键盘快捷键

- **Ctrl+O**：添加文件（文件菜单）
- **Delete**：删除选中文件
- **双击文件**：加载预览
- **F5**：刷新预览

---

**提示**：界面已优化为"选择即预览"模式，无需手动点击预览按钮！ 