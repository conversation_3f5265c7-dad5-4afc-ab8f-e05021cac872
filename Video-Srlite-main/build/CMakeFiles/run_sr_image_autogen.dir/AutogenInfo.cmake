# Meta
set(AM_MULTI_CONFIG "SINGLE")
# Directories and files
set(AM_CMAKE_BINARY_DIR "/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build/")
set(AM_CMAKE_SOURCE_DIR "/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/")
set(AM_CMAKE_CURRENT_SOURCE_DIR "/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/")
set(AM_CMAKE_CURRENT_BINARY_DIR "/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build/")
set(AM_CMAKE_INCLUDE_DIRECTORIES_PROJECT_BEFORE "")
set(AM_BUILD_DIR "/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build/run_sr_image_autogen")
set(AM_SOURCES "/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Processing/SuperResolution.cpp;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/SuperEigen/src/ModelSession.cpp;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/SuperEigen/src/PrePostProcessor.cpp;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/SuperEigen/src/SuperResConfig.cpp;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/SuperEigen/src/SuperResEngine.cpp;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Utils/LogUtils.cpp;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Utils/Logger.cpp;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/tools/run_sr_image.cpp")
set(AM_HEADERS "")
# Qt environment
set(AM_QT_VERSION_MAJOR "5")
set(AM_QT_VERSION_MINOR "9")
set(AM_QT_MOC_EXECUTABLE "/usr/lib/qt5/bin/moc")
set(AM_QT_UIC_EXECUTABLE "/usr/lib/qt5/bin/uic")
set(AM_QT_RCC_EXECUTABLE "/usr/lib/qt5/bin/rcc")
# MOC settings
set(AM_MOC_SKIP "/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build/VideoSRLiteGUI_autogen/mocs_compilation.cpp;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build/run_sr_image_autogen/mocs_compilation.cpp")
set(AM_MOC_DEFINITIONS "HAVE_ONNXRUNTIME")
set(AM_MOC_INCLUDES "/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build/run_sr_image_autogen/include;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Processing;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/UI;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Utils;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/DataStruct;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Decoder/include;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/SuperEigen/include;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/AppController;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/AudioProcessor;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/AudioProc;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Encoder;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/SyncVA;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/PostFilter;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/WorkerPool;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Core;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/onnx/onnxruntime-linux-x64-1.15.1/include;/usr/include/opencv;/usr/include")
set(AM_MOC_OPTIONS "")
set(AM_MOC_RELAXED_MODE "FALSE")
set(AM_MOC_MACRO_NAMES "Q_OBJECT;Q_GADGET;Q_NAMESPACE")
set(AM_MOC_DEPEND_FILTERS "")
set(AM_MOC_PREDEFS_CMD "/usr/bin/c++;-dM;-E;-c;/usr/share/cmake-3.10/Modules/CMakeCXXCompilerABI.cpp")
# UIC settings
set(AM_UIC_SKIP "/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build/VideoSRLiteGUI_autogen/mocs_compilation.cpp;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build/run_sr_image_autogen/mocs_compilation.cpp")
set(AM_UIC_TARGET_OPTIONS "")
set(AM_UIC_OPTIONS_FILES "")
set(AM_UIC_OPTIONS_OPTIONS "")
set(AM_UIC_SEARCH_PATHS "/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/UI")
# RCC settings
set(AM_RCC_SOURCES "")
set(AM_RCC_BUILDS "")
set(AM_RCC_OPTIONS "")
set(AM_RCC_INPUTS "")
# Configurations options
set(AM_CONFIG_SUFFIX_Release "_Release")
