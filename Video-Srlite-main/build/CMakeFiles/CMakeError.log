Performing C SOURCE FILE Test CMAKE_HAVE_LIBC_PTHREAD failed with the following output:
Change Dir: /home/<USER>/Public/video_lite/VideoSR-Lite/build/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/make cmTC_8b93b/fast && /usr/bin/make -f CMakeFiles/cmTC_8b93b.dir/build.make CMakeFiles/cmTC_8b93b.dir/build
make[1]: 进入目录“/home/<USER>/Public/video_lite/VideoSR-Lite/build/CMakeFiles/CMakeTmp”
Building C object CMakeFiles/cmTC_8b93b.dir/src.c.o
/usr/bin/cc   -DCMAKE_HAVE_LIBC_PTHREAD   -o CMakeFiles/cmTC_8b93b.dir/src.c.o   -c /home/<USER>/Public/video_lite/VideoSR-Lite/build/CMakeFiles/CMakeTmp/src.c
Linking C executable cmTC_8b93b
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_8b93b.dir/link.txt --verbose=1
/usr/bin/cc  -DCMAKE_HAVE_LIBC_PTHREAD    CMakeFiles/cmTC_8b93b.dir/src.c.o  -o cmTC_8b93b 
/usr/bin/ld: CMakeFiles/cmTC_8b93b.dir/src.c.o: in function `main':
src.c:(.text+0x46): undefined reference to `pthread_create'
/usr/bin/ld: src.c:(.text+0x52): undefined reference to `pthread_detach'
/usr/bin/ld: src.c:(.text+0x63): undefined reference to `pthread_join'
collect2: error: ld returned 1 exit status
make[1]: *** [CMakeFiles/cmTC_8b93b.dir/build.make:87：cmTC_8b93b] 错误 1
make[1]: 离开目录“/home/<USER>/Public/video_lite/VideoSR-Lite/build/CMakeFiles/CMakeTmp”
make: *** [Makefile:121：cmTC_8b93b/fast] 错误 2


Source file was:
#include <pthread.h>

void* test_func(void* data)
{
  return data;
}

int main(void)
{
  pthread_t thread;
  pthread_create(&thread, NULL, test_func, NULL);
  pthread_detach(thread);
  pthread_join(thread, NULL);
  pthread_atfork(NULL, NULL, NULL);
  pthread_exit(NULL);

  return 0;
}

Determining if the function pthread_create exists in the pthreads failed with the following output:
Change Dir: /home/<USER>/Public/video_lite/VideoSR-Lite/build/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/make cmTC_911fa/fast && /usr/bin/make -f CMakeFiles/cmTC_911fa.dir/build.make CMakeFiles/cmTC_911fa.dir/build
make[1]: 进入目录“/home/<USER>/Public/video_lite/VideoSR-Lite/build/CMakeFiles/CMakeTmp”
Building C object CMakeFiles/cmTC_911fa.dir/CheckFunctionExists.c.o
/usr/bin/cc   -DCHECK_FUNCTION_EXISTS=pthread_create   -o CMakeFiles/cmTC_911fa.dir/CheckFunctionExists.c.o   -c /usr/share/cmake-3.16/Modules/CheckFunctionExists.c
Linking C executable cmTC_911fa
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_911fa.dir/link.txt --verbose=1
/usr/bin/cc  -DCHECK_FUNCTION_EXISTS=pthread_create    CMakeFiles/cmTC_911fa.dir/CheckFunctionExists.c.o  -o cmTC_911fa  -lpthreads 
/usr/bin/ld: 找不到 -lpthreads
collect2: error: ld returned 1 exit status
make[1]: *** [CMakeFiles/cmTC_911fa.dir/build.make:87：cmTC_911fa] 错误 1
make[1]: 离开目录“/home/<USER>/Public/video_lite/VideoSR-Lite/build/CMakeFiles/CMakeTmp”
make: *** [Makefile:121：cmTC_911fa/fast] 错误 2



