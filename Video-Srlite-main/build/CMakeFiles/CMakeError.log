Determining if the pthread_create exist failed with the following output:
Change Dir: /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build/CMakeFiles/CMakeTmp

Run Build Command:"/usr/bin/make" "cmTC_69912/fast"
/usr/bin/make -f CMakeFiles/cmTC_69912.dir/build.make CMakeFiles/cmTC_69912.dir/build
make[1]: 进入目录“/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build/CMakeFiles/CMakeTmp”
Building C object CMakeFiles/cmTC_69912.dir/CheckSymbolExists.c.o
/usr/bin/cc    -o CMakeFiles/cmTC_69912.dir/CheckSymbolExists.c.o   -c /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build/CMakeFiles/CMakeTmp/CheckSymbolExists.c
Linking C executable cmTC_69912
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_69912.dir/link.txt --verbose=1
/usr/bin/cc      CMakeFiles/cmTC_69912.dir/CheckSymbolExists.c.o  -o cmTC_69912 
CMakeFiles/cmTC_69912.dir/CheckSymbolExists.c.o：在函数‘main’中：
CheckSymbolExists.c:(.text+0x1b)：对‘pthread_create’未定义的引用
collect2: error: ld returned 1 exit status
CMakeFiles/cmTC_69912.dir/build.make:97: recipe for target 'cmTC_69912' failed
make[1]: *** [cmTC_69912] Error 1
make[1]: 离开目录“/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build/CMakeFiles/CMakeTmp”
Makefile:126: recipe for target 'cmTC_69912/fast' failed
make: *** [cmTC_69912/fast] Error 2

File /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build/CMakeFiles/CMakeTmp/CheckSymbolExists.c:
/* */
#include <pthread.h>

int main(int argc, char** argv)
{
  (void)argv;
#ifndef pthread_create
  return ((int*)(&pthread_create))[argc];
#else
  (void)argc;
  return 0;
#endif
}

Determining if the function pthread_create exists in the pthreads failed with the following output:
Change Dir: /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build/CMakeFiles/CMakeTmp

Run Build Command:"/usr/bin/make" "cmTC_192ff/fast"
/usr/bin/make -f CMakeFiles/cmTC_192ff.dir/build.make CMakeFiles/cmTC_192ff.dir/build
make[1]: 进入目录“/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build/CMakeFiles/CMakeTmp”
Building C object CMakeFiles/cmTC_192ff.dir/CheckFunctionExists.c.o
/usr/bin/cc   -DCHECK_FUNCTION_EXISTS=pthread_create   -o CMakeFiles/cmTC_192ff.dir/CheckFunctionExists.c.o   -c /usr/share/cmake-3.10/Modules/CheckFunctionExists.c
Linking C executable cmTC_192ff
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_192ff.dir/link.txt --verbose=1
/usr/bin/cc  -DCHECK_FUNCTION_EXISTS=pthread_create    CMakeFiles/cmTC_192ff.dir/CheckFunctionExists.c.o  -o cmTC_192ff -lpthreads 
/usr/bin/ld: 找不到 -lpthreads
collect2: error: ld returned 1 exit status
CMakeFiles/cmTC_192ff.dir/build.make:97: recipe for target 'cmTC_192ff' failed
make[1]: *** [cmTC_192ff] Error 1
make[1]: 离开目录“/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build/CMakeFiles/CMakeTmp”
Makefile:126: recipe for target 'cmTC_192ff/fast' failed
make: *** [cmTC_192ff/fast] Error 2


