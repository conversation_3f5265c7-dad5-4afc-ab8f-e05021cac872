Determining if the pthread_create exist failed with the following output:
Change Dir: /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build/CMakeFiles/CMakeTmp

Run Build Command:"/usr/bin/make" "cmTC_8c15a/fast"
/usr/bin/make -f CMakeFiles/cmTC_8c15a.dir/build.make CMakeFiles/cmTC_8c15a.dir/build
make[1]: 进入目录“/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build/CMakeFiles/CMakeTmp”
Building C object CMakeFiles/cmTC_8c15a.dir/CheckSymbolExists.c.o
/usr/bin/cc    -o CMakeFiles/cmTC_8c15a.dir/CheckSymbolExists.c.o   -c /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build/CMakeFiles/CMakeTmp/CheckSymbolExists.c
Linking C executable cmTC_8c15a
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_8c15a.dir/link.txt --verbose=1
/usr/bin/cc      CMakeFiles/cmTC_8c15a.dir/CheckSymbolExists.c.o  -o cmTC_8c15a 
CMakeFiles/cmTC_8c15a.dir/CheckSymbolExists.c.o：在函数‘main’中：
CheckSymbolExists.c:(.text+0x1b)：对‘pthread_create’未定义的引用
collect2: error: ld returned 1 exit status
CMakeFiles/cmTC_8c15a.dir/build.make:97: recipe for target 'cmTC_8c15a' failed
make[1]: *** [cmTC_8c15a] Error 1
make[1]: 离开目录“/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build/CMakeFiles/CMakeTmp”
Makefile:126: recipe for target 'cmTC_8c15a/fast' failed
make: *** [cmTC_8c15a/fast] Error 2

File /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build/CMakeFiles/CMakeTmp/CheckSymbolExists.c:
/* */
#include <pthread.h>

int main(int argc, char** argv)
{
  (void)argv;
#ifndef pthread_create
  return ((int*)(&pthread_create))[argc];
#else
  (void)argc;
  return 0;
#endif
}

Determining if the function pthread_create exists in the pthreads failed with the following output:
Change Dir: /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build/CMakeFiles/CMakeTmp

Run Build Command:"/usr/bin/make" "cmTC_af5d6/fast"
/usr/bin/make -f CMakeFiles/cmTC_af5d6.dir/build.make CMakeFiles/cmTC_af5d6.dir/build
make[1]: 进入目录“/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build/CMakeFiles/CMakeTmp”
Building C object CMakeFiles/cmTC_af5d6.dir/CheckFunctionExists.c.o
/usr/bin/cc   -DCHECK_FUNCTION_EXISTS=pthread_create   -o CMakeFiles/cmTC_af5d6.dir/CheckFunctionExists.c.o   -c /usr/share/cmake-3.10/Modules/CheckFunctionExists.c
Linking C executable cmTC_af5d6
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_af5d6.dir/link.txt --verbose=1
/usr/bin/cc  -DCHECK_FUNCTION_EXISTS=pthread_create    CMakeFiles/cmTC_af5d6.dir/CheckFunctionExists.c.o  -o cmTC_af5d6 -lpthreads 
/usr/bin/ld: 找不到 -lpthreads
collect2: error: ld returned 1 exit status
CMakeFiles/cmTC_af5d6.dir/build.make:97: recipe for target 'cmTC_af5d6' failed
make[1]: *** [cmTC_af5d6] Error 1
make[1]: 离开目录“/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build/CMakeFiles/CMakeTmp”
Makefile:126: recipe for target 'cmTC_af5d6/fast' failed
make: *** [cmTC_af5d6/fast] Error 2


