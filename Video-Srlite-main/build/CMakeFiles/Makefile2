# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Default target executed when no arguments are given to make.
default_target: all

.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Public/video_lite/VideoSR-Lite

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Public/video_lite/VideoSR-Lite/build

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/run_sr_image.dir/all
all: CMakeFiles/VideoSRLiteGUI.dir/all

.PHONY : all

# The main recursive "preinstall" target.
preinstall:

.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/run_sr_image.dir/clean
clean: CMakeFiles/VideoSRLiteGUI.dir/clean
clean: CMakeFiles/run_sr_image_autogen.dir/clean
clean: CMakeFiles/VideoSRLiteGUI_autogen.dir/clean

.PHONY : clean

#=============================================================================
# Target rules for target CMakeFiles/run_sr_image.dir

# All Build rule for target.
CMakeFiles/run_sr_image.dir/all: CMakeFiles/run_sr_image_autogen.dir/all
	$(MAKE) -f CMakeFiles/run_sr_image.dir/build.make CMakeFiles/run_sr_image.dir/depend
	$(MAKE) -f CMakeFiles/run_sr_image.dir/build.make CMakeFiles/run_sr_image.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/Public/video_lite/VideoSR-Lite/build/CMakeFiles --progress-num=35,36,37,38,39,40,41,42,43,44 "Built target run_sr_image"
.PHONY : CMakeFiles/run_sr_image.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/run_sr_image.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Public/video_lite/VideoSR-Lite/build/CMakeFiles 11
	$(MAKE) -f CMakeFiles/Makefile2 CMakeFiles/run_sr_image.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Public/video_lite/VideoSR-Lite/build/CMakeFiles 0
.PHONY : CMakeFiles/run_sr_image.dir/rule

# Convenience name for target.
run_sr_image: CMakeFiles/run_sr_image.dir/rule

.PHONY : run_sr_image

# clean rule for target.
CMakeFiles/run_sr_image.dir/clean:
	$(MAKE) -f CMakeFiles/run_sr_image.dir/build.make CMakeFiles/run_sr_image.dir/clean
.PHONY : CMakeFiles/run_sr_image.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/VideoSRLiteGUI.dir

# All Build rule for target.
CMakeFiles/VideoSRLiteGUI.dir/all: CMakeFiles/VideoSRLiteGUI_autogen.dir/all
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/depend
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/Public/video_lite/VideoSR-Lite/build/CMakeFiles --progress-num=1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33 "Built target VideoSRLiteGUI"
.PHONY : CMakeFiles/VideoSRLiteGUI.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/VideoSRLiteGUI.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Public/video_lite/VideoSR-Lite/build/CMakeFiles 34
	$(MAKE) -f CMakeFiles/Makefile2 CMakeFiles/VideoSRLiteGUI.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Public/video_lite/VideoSR-Lite/build/CMakeFiles 0
.PHONY : CMakeFiles/VideoSRLiteGUI.dir/rule

# Convenience name for target.
VideoSRLiteGUI: CMakeFiles/VideoSRLiteGUI.dir/rule

.PHONY : VideoSRLiteGUI

# clean rule for target.
CMakeFiles/VideoSRLiteGUI.dir/clean:
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/clean
.PHONY : CMakeFiles/VideoSRLiteGUI.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/run_sr_image_autogen.dir

# All Build rule for target.
CMakeFiles/run_sr_image_autogen.dir/all:
	$(MAKE) -f CMakeFiles/run_sr_image_autogen.dir/build.make CMakeFiles/run_sr_image_autogen.dir/depend
	$(MAKE) -f CMakeFiles/run_sr_image_autogen.dir/build.make CMakeFiles/run_sr_image_autogen.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/Public/video_lite/VideoSR-Lite/build/CMakeFiles --progress-num=45 "Built target run_sr_image_autogen"
.PHONY : CMakeFiles/run_sr_image_autogen.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/run_sr_image_autogen.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Public/video_lite/VideoSR-Lite/build/CMakeFiles 1
	$(MAKE) -f CMakeFiles/Makefile2 CMakeFiles/run_sr_image_autogen.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Public/video_lite/VideoSR-Lite/build/CMakeFiles 0
.PHONY : CMakeFiles/run_sr_image_autogen.dir/rule

# Convenience name for target.
run_sr_image_autogen: CMakeFiles/run_sr_image_autogen.dir/rule

.PHONY : run_sr_image_autogen

# clean rule for target.
CMakeFiles/run_sr_image_autogen.dir/clean:
	$(MAKE) -f CMakeFiles/run_sr_image_autogen.dir/build.make CMakeFiles/run_sr_image_autogen.dir/clean
.PHONY : CMakeFiles/run_sr_image_autogen.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/VideoSRLiteGUI_autogen.dir

# All Build rule for target.
CMakeFiles/VideoSRLiteGUI_autogen.dir/all:
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI_autogen.dir/build.make CMakeFiles/VideoSRLiteGUI_autogen.dir/depend
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI_autogen.dir/build.make CMakeFiles/VideoSRLiteGUI_autogen.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/Public/video_lite/VideoSR-Lite/build/CMakeFiles --progress-num=34 "Built target VideoSRLiteGUI_autogen"
.PHONY : CMakeFiles/VideoSRLiteGUI_autogen.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/VideoSRLiteGUI_autogen.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Public/video_lite/VideoSR-Lite/build/CMakeFiles 1
	$(MAKE) -f CMakeFiles/Makefile2 CMakeFiles/VideoSRLiteGUI_autogen.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Public/video_lite/VideoSR-Lite/build/CMakeFiles 0
.PHONY : CMakeFiles/VideoSRLiteGUI_autogen.dir/rule

# Convenience name for target.
VideoSRLiteGUI_autogen: CMakeFiles/VideoSRLiteGUI_autogen.dir/rule

.PHONY : VideoSRLiteGUI_autogen

# clean rule for target.
CMakeFiles/VideoSRLiteGUI_autogen.dir/clean:
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI_autogen.dir/build.make CMakeFiles/VideoSRLiteGUI_autogen.dir/clean
.PHONY : CMakeFiles/VideoSRLiteGUI_autogen.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

