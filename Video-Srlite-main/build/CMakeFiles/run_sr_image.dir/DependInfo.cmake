# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  "CXX"
  )
# The set of files for implicit dependencies of each language:
set(CMAKE_DEPENDS_CHECK_CXX
  "/home/<USER>/Public/video_lite/VideoSR-Lite/build/run_sr_image_autogen/mocs_compilation.cpp" "/home/<USER>/Public/video_lite/VideoSR-Lite/build/CMakeFiles/run_sr_image.dir/run_sr_image_autogen/mocs_compilation.cpp.o"
  "/home/<USER>/Public/video_lite/VideoSR-Lite/src/Processing/SuperResolution.cpp" "/home/<USER>/Public/video_lite/VideoSR-Lite/build/CMakeFiles/run_sr_image.dir/src/Processing/SuperResolution.cpp.o"
  "/home/<USER>/Public/video_lite/VideoSR-Lite/src/SuperEigen/src/ModelSession.cpp" "/home/<USER>/Public/video_lite/VideoSR-Lite/build/CMakeFiles/run_sr_image.dir/src/SuperEigen/src/ModelSession.cpp.o"
  "/home/<USER>/Public/video_lite/VideoSR-Lite/src/SuperEigen/src/PrePostProcessor.cpp" "/home/<USER>/Public/video_lite/VideoSR-Lite/build/CMakeFiles/run_sr_image.dir/src/SuperEigen/src/PrePostProcessor.cpp.o"
  "/home/<USER>/Public/video_lite/VideoSR-Lite/src/SuperEigen/src/SuperResConfig.cpp" "/home/<USER>/Public/video_lite/VideoSR-Lite/build/CMakeFiles/run_sr_image.dir/src/SuperEigen/src/SuperResConfig.cpp.o"
  "/home/<USER>/Public/video_lite/VideoSR-Lite/src/SuperEigen/src/SuperResEngine.cpp" "/home/<USER>/Public/video_lite/VideoSR-Lite/build/CMakeFiles/run_sr_image.dir/src/SuperEigen/src/SuperResEngine.cpp.o"
  "/home/<USER>/Public/video_lite/VideoSR-Lite/src/Utils/LogUtils.cpp" "/home/<USER>/Public/video_lite/VideoSR-Lite/build/CMakeFiles/run_sr_image.dir/src/Utils/LogUtils.cpp.o"
  "/home/<USER>/Public/video_lite/VideoSR-Lite/src/Utils/Logger.cpp" "/home/<USER>/Public/video_lite/VideoSR-Lite/build/CMakeFiles/run_sr_image.dir/src/Utils/Logger.cpp.o"
  "/home/<USER>/Public/video_lite/VideoSR-Lite/src/tools/run_sr_image.cpp" "/home/<USER>/Public/video_lite/VideoSR-Lite/build/CMakeFiles/run_sr_image.dir/src/tools/run_sr_image.cpp.o"
  )
set(CMAKE_CXX_COMPILER_ID "GNU")

# Preprocessor definitions for this target.
set(CMAKE_TARGET_DEFINITIONS_CXX
  "HAVE_ONNXRUNTIME"
  )

# The include file search paths:
set(CMAKE_CXX_TARGET_INCLUDE_PATH
  "run_sr_image_autogen/include"
  "../src"
  "../src/Processing"
  "../src/UI"
  "../src/Utils"
  "../src/DataStruct"
  "../src/Decoder/include"
  "../src/SuperEigen/include"
  "../src/AppController"
  "../src/AudioProcessor"
  "../src/AudioProc"
  "../src/Encoder"
  "../src/SyncVA"
  "../src/PostFilter"
  "../src/WorkerPool"
  "../src/Core"
  "../onnx/onnxruntime-linux-x64-1.15.1/include"
  "/usr/include/opencv4"
  )

# Targets to which this target links.
set(CMAKE_TARGET_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
