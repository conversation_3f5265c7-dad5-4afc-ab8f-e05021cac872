# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# compile CXX with /usr/bin/c++
CXX_FLAGS = -O3 -DNDEBUG   -std=gnu++17

CXX_DEFINES = -DHAVE_ONNXRUNTIME

CXX_INCLUDES = -I/home/<USER>/Public/video_lite/VideoSR-Lite/build/run_sr_image_autogen/include -I/home/<USER>/Public/video_lite/VideoSR-Lite/src -I/home/<USER>/Public/video_lite/VideoSR-Lite/src/Processing -I/home/<USER>/Public/video_lite/VideoSR-Lite/src/UI -I/home/<USER>/Public/video_lite/VideoSR-Lite/src/Utils -I/home/<USER>/Public/video_lite/VideoSR-Lite/src/DataStruct -I/home/<USER>/Public/video_lite/VideoSR-Lite/src/Decoder/include -I/home/<USER>/Public/video_lite/VideoSR-Lite/src/SuperEigen/include -I/home/<USER>/Public/video_lite/VideoSR-Lite/src/AppController -I/home/<USER>/Public/video_lite/VideoSR-Lite/src/AudioProcessor -I/home/<USER>/Public/video_lite/VideoSR-Lite/src/AudioProc -I/home/<USER>/Public/video_lite/VideoSR-Lite/src/Encoder -I/home/<USER>/Public/video_lite/VideoSR-Lite/src/SyncVA -I/home/<USER>/Public/video_lite/VideoSR-Lite/src/PostFilter -I/home/<USER>/Public/video_lite/VideoSR-Lite/src/WorkerPool -I/home/<USER>/Public/video_lite/VideoSR-Lite/src/Core -I/home/<USER>/Public/video_lite/VideoSR-Lite/onnx/onnxruntime-linux-x64-1.15.1/include -isystem /usr/include/opencv4 

