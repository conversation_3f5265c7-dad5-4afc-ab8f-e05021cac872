# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.10

# compile CXX with g++-8
CXX_FLAGS = -O3 -DNDEBUG   -std=gnu++1z

CXX_DEFINES = -DHAVE_ONNXRUNTIME

CXX_INCLUDES = -I/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build/run_sr_image_autogen/include -I/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src -I/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Processing -I/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/UI -I/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Utils -I/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/DataStruct -I/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Decoder/include -I/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/SuperEigen/include -I/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/AppController -I/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/AudioProcessor -I/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/AudioProc -I/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Encoder -I/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/SyncVA -I/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/PostFilter -I/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/WorkerPool -I/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Core -I/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/onnx/onnxruntime-linux-x64-1.15.1/include -isystem /usr/include/opencv 

