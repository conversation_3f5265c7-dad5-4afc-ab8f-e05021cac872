# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.10

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build

# Include any dependencies generated for this target.
include CMakeFiles/run_sr_image.dir/depend.make

# Include the progress variables for this target.
include CMakeFiles/run_sr_image.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/run_sr_image.dir/flags.make

CMakeFiles/run_sr_image.dir/src/tools/run_sr_image.cpp.o: CMakeFiles/run_sr_image.dir/flags.make
CMakeFiles/run_sr_image.dir/src/tools/run_sr_image.cpp.o: ../src/tools/run_sr_image.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/run_sr_image.dir/src/tools/run_sr_image.cpp.o"
	g++-8  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/run_sr_image.dir/src/tools/run_sr_image.cpp.o -c /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/tools/run_sr_image.cpp

CMakeFiles/run_sr_image.dir/src/tools/run_sr_image.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/run_sr_image.dir/src/tools/run_sr_image.cpp.i"
	g++-8 $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/tools/run_sr_image.cpp > CMakeFiles/run_sr_image.dir/src/tools/run_sr_image.cpp.i

CMakeFiles/run_sr_image.dir/src/tools/run_sr_image.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/run_sr_image.dir/src/tools/run_sr_image.cpp.s"
	g++-8 $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/tools/run_sr_image.cpp -o CMakeFiles/run_sr_image.dir/src/tools/run_sr_image.cpp.s

CMakeFiles/run_sr_image.dir/src/tools/run_sr_image.cpp.o.requires:

.PHONY : CMakeFiles/run_sr_image.dir/src/tools/run_sr_image.cpp.o.requires

CMakeFiles/run_sr_image.dir/src/tools/run_sr_image.cpp.o.provides: CMakeFiles/run_sr_image.dir/src/tools/run_sr_image.cpp.o.requires
	$(MAKE) -f CMakeFiles/run_sr_image.dir/build.make CMakeFiles/run_sr_image.dir/src/tools/run_sr_image.cpp.o.provides.build
.PHONY : CMakeFiles/run_sr_image.dir/src/tools/run_sr_image.cpp.o.provides

CMakeFiles/run_sr_image.dir/src/tools/run_sr_image.cpp.o.provides.build: CMakeFiles/run_sr_image.dir/src/tools/run_sr_image.cpp.o


CMakeFiles/run_sr_image.dir/src/Processing/SuperResolution.cpp.o: CMakeFiles/run_sr_image.dir/flags.make
CMakeFiles/run_sr_image.dir/src/Processing/SuperResolution.cpp.o: ../src/Processing/SuperResolution.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/run_sr_image.dir/src/Processing/SuperResolution.cpp.o"
	g++-8  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/run_sr_image.dir/src/Processing/SuperResolution.cpp.o -c /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Processing/SuperResolution.cpp

CMakeFiles/run_sr_image.dir/src/Processing/SuperResolution.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/run_sr_image.dir/src/Processing/SuperResolution.cpp.i"
	g++-8 $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Processing/SuperResolution.cpp > CMakeFiles/run_sr_image.dir/src/Processing/SuperResolution.cpp.i

CMakeFiles/run_sr_image.dir/src/Processing/SuperResolution.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/run_sr_image.dir/src/Processing/SuperResolution.cpp.s"
	g++-8 $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Processing/SuperResolution.cpp -o CMakeFiles/run_sr_image.dir/src/Processing/SuperResolution.cpp.s

CMakeFiles/run_sr_image.dir/src/Processing/SuperResolution.cpp.o.requires:

.PHONY : CMakeFiles/run_sr_image.dir/src/Processing/SuperResolution.cpp.o.requires

CMakeFiles/run_sr_image.dir/src/Processing/SuperResolution.cpp.o.provides: CMakeFiles/run_sr_image.dir/src/Processing/SuperResolution.cpp.o.requires
	$(MAKE) -f CMakeFiles/run_sr_image.dir/build.make CMakeFiles/run_sr_image.dir/src/Processing/SuperResolution.cpp.o.provides.build
.PHONY : CMakeFiles/run_sr_image.dir/src/Processing/SuperResolution.cpp.o.provides

CMakeFiles/run_sr_image.dir/src/Processing/SuperResolution.cpp.o.provides.build: CMakeFiles/run_sr_image.dir/src/Processing/SuperResolution.cpp.o


CMakeFiles/run_sr_image.dir/src/SuperEigen/src/SuperResEngine.cpp.o: CMakeFiles/run_sr_image.dir/flags.make
CMakeFiles/run_sr_image.dir/src/SuperEigen/src/SuperResEngine.cpp.o: ../src/SuperEigen/src/SuperResEngine.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object CMakeFiles/run_sr_image.dir/src/SuperEigen/src/SuperResEngine.cpp.o"
	g++-8  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/run_sr_image.dir/src/SuperEigen/src/SuperResEngine.cpp.o -c /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/SuperEigen/src/SuperResEngine.cpp

CMakeFiles/run_sr_image.dir/src/SuperEigen/src/SuperResEngine.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/run_sr_image.dir/src/SuperEigen/src/SuperResEngine.cpp.i"
	g++-8 $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/SuperEigen/src/SuperResEngine.cpp > CMakeFiles/run_sr_image.dir/src/SuperEigen/src/SuperResEngine.cpp.i

CMakeFiles/run_sr_image.dir/src/SuperEigen/src/SuperResEngine.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/run_sr_image.dir/src/SuperEigen/src/SuperResEngine.cpp.s"
	g++-8 $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/SuperEigen/src/SuperResEngine.cpp -o CMakeFiles/run_sr_image.dir/src/SuperEigen/src/SuperResEngine.cpp.s

CMakeFiles/run_sr_image.dir/src/SuperEigen/src/SuperResEngine.cpp.o.requires:

.PHONY : CMakeFiles/run_sr_image.dir/src/SuperEigen/src/SuperResEngine.cpp.o.requires

CMakeFiles/run_sr_image.dir/src/SuperEigen/src/SuperResEngine.cpp.o.provides: CMakeFiles/run_sr_image.dir/src/SuperEigen/src/SuperResEngine.cpp.o.requires
	$(MAKE) -f CMakeFiles/run_sr_image.dir/build.make CMakeFiles/run_sr_image.dir/src/SuperEigen/src/SuperResEngine.cpp.o.provides.build
.PHONY : CMakeFiles/run_sr_image.dir/src/SuperEigen/src/SuperResEngine.cpp.o.provides

CMakeFiles/run_sr_image.dir/src/SuperEigen/src/SuperResEngine.cpp.o.provides.build: CMakeFiles/run_sr_image.dir/src/SuperEigen/src/SuperResEngine.cpp.o


CMakeFiles/run_sr_image.dir/src/SuperEigen/src/ModelSession.cpp.o: CMakeFiles/run_sr_image.dir/flags.make
CMakeFiles/run_sr_image.dir/src/SuperEigen/src/ModelSession.cpp.o: ../src/SuperEigen/src/ModelSession.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object CMakeFiles/run_sr_image.dir/src/SuperEigen/src/ModelSession.cpp.o"
	g++-8  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/run_sr_image.dir/src/SuperEigen/src/ModelSession.cpp.o -c /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/SuperEigen/src/ModelSession.cpp

CMakeFiles/run_sr_image.dir/src/SuperEigen/src/ModelSession.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/run_sr_image.dir/src/SuperEigen/src/ModelSession.cpp.i"
	g++-8 $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/SuperEigen/src/ModelSession.cpp > CMakeFiles/run_sr_image.dir/src/SuperEigen/src/ModelSession.cpp.i

CMakeFiles/run_sr_image.dir/src/SuperEigen/src/ModelSession.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/run_sr_image.dir/src/SuperEigen/src/ModelSession.cpp.s"
	g++-8 $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/SuperEigen/src/ModelSession.cpp -o CMakeFiles/run_sr_image.dir/src/SuperEigen/src/ModelSession.cpp.s

CMakeFiles/run_sr_image.dir/src/SuperEigen/src/ModelSession.cpp.o.requires:

.PHONY : CMakeFiles/run_sr_image.dir/src/SuperEigen/src/ModelSession.cpp.o.requires

CMakeFiles/run_sr_image.dir/src/SuperEigen/src/ModelSession.cpp.o.provides: CMakeFiles/run_sr_image.dir/src/SuperEigen/src/ModelSession.cpp.o.requires
	$(MAKE) -f CMakeFiles/run_sr_image.dir/build.make CMakeFiles/run_sr_image.dir/src/SuperEigen/src/ModelSession.cpp.o.provides.build
.PHONY : CMakeFiles/run_sr_image.dir/src/SuperEigen/src/ModelSession.cpp.o.provides

CMakeFiles/run_sr_image.dir/src/SuperEigen/src/ModelSession.cpp.o.provides.build: CMakeFiles/run_sr_image.dir/src/SuperEigen/src/ModelSession.cpp.o


CMakeFiles/run_sr_image.dir/src/SuperEigen/src/PrePostProcessor.cpp.o: CMakeFiles/run_sr_image.dir/flags.make
CMakeFiles/run_sr_image.dir/src/SuperEigen/src/PrePostProcessor.cpp.o: ../src/SuperEigen/src/PrePostProcessor.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object CMakeFiles/run_sr_image.dir/src/SuperEigen/src/PrePostProcessor.cpp.o"
	g++-8  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/run_sr_image.dir/src/SuperEigen/src/PrePostProcessor.cpp.o -c /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/SuperEigen/src/PrePostProcessor.cpp

CMakeFiles/run_sr_image.dir/src/SuperEigen/src/PrePostProcessor.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/run_sr_image.dir/src/SuperEigen/src/PrePostProcessor.cpp.i"
	g++-8 $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/SuperEigen/src/PrePostProcessor.cpp > CMakeFiles/run_sr_image.dir/src/SuperEigen/src/PrePostProcessor.cpp.i

CMakeFiles/run_sr_image.dir/src/SuperEigen/src/PrePostProcessor.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/run_sr_image.dir/src/SuperEigen/src/PrePostProcessor.cpp.s"
	g++-8 $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/SuperEigen/src/PrePostProcessor.cpp -o CMakeFiles/run_sr_image.dir/src/SuperEigen/src/PrePostProcessor.cpp.s

CMakeFiles/run_sr_image.dir/src/SuperEigen/src/PrePostProcessor.cpp.o.requires:

.PHONY : CMakeFiles/run_sr_image.dir/src/SuperEigen/src/PrePostProcessor.cpp.o.requires

CMakeFiles/run_sr_image.dir/src/SuperEigen/src/PrePostProcessor.cpp.o.provides: CMakeFiles/run_sr_image.dir/src/SuperEigen/src/PrePostProcessor.cpp.o.requires
	$(MAKE) -f CMakeFiles/run_sr_image.dir/build.make CMakeFiles/run_sr_image.dir/src/SuperEigen/src/PrePostProcessor.cpp.o.provides.build
.PHONY : CMakeFiles/run_sr_image.dir/src/SuperEigen/src/PrePostProcessor.cpp.o.provides

CMakeFiles/run_sr_image.dir/src/SuperEigen/src/PrePostProcessor.cpp.o.provides.build: CMakeFiles/run_sr_image.dir/src/SuperEigen/src/PrePostProcessor.cpp.o


CMakeFiles/run_sr_image.dir/src/SuperEigen/src/SuperResConfig.cpp.o: CMakeFiles/run_sr_image.dir/flags.make
CMakeFiles/run_sr_image.dir/src/SuperEigen/src/SuperResConfig.cpp.o: ../src/SuperEigen/src/SuperResConfig.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building CXX object CMakeFiles/run_sr_image.dir/src/SuperEigen/src/SuperResConfig.cpp.o"
	g++-8  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/run_sr_image.dir/src/SuperEigen/src/SuperResConfig.cpp.o -c /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/SuperEigen/src/SuperResConfig.cpp

CMakeFiles/run_sr_image.dir/src/SuperEigen/src/SuperResConfig.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/run_sr_image.dir/src/SuperEigen/src/SuperResConfig.cpp.i"
	g++-8 $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/SuperEigen/src/SuperResConfig.cpp > CMakeFiles/run_sr_image.dir/src/SuperEigen/src/SuperResConfig.cpp.i

CMakeFiles/run_sr_image.dir/src/SuperEigen/src/SuperResConfig.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/run_sr_image.dir/src/SuperEigen/src/SuperResConfig.cpp.s"
	g++-8 $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/SuperEigen/src/SuperResConfig.cpp -o CMakeFiles/run_sr_image.dir/src/SuperEigen/src/SuperResConfig.cpp.s

CMakeFiles/run_sr_image.dir/src/SuperEigen/src/SuperResConfig.cpp.o.requires:

.PHONY : CMakeFiles/run_sr_image.dir/src/SuperEigen/src/SuperResConfig.cpp.o.requires

CMakeFiles/run_sr_image.dir/src/SuperEigen/src/SuperResConfig.cpp.o.provides: CMakeFiles/run_sr_image.dir/src/SuperEigen/src/SuperResConfig.cpp.o.requires
	$(MAKE) -f CMakeFiles/run_sr_image.dir/build.make CMakeFiles/run_sr_image.dir/src/SuperEigen/src/SuperResConfig.cpp.o.provides.build
.PHONY : CMakeFiles/run_sr_image.dir/src/SuperEigen/src/SuperResConfig.cpp.o.provides

CMakeFiles/run_sr_image.dir/src/SuperEigen/src/SuperResConfig.cpp.o.provides.build: CMakeFiles/run_sr_image.dir/src/SuperEigen/src/SuperResConfig.cpp.o


CMakeFiles/run_sr_image.dir/src/Utils/Logger.cpp.o: CMakeFiles/run_sr_image.dir/flags.make
CMakeFiles/run_sr_image.dir/src/Utils/Logger.cpp.o: ../src/Utils/Logger.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building CXX object CMakeFiles/run_sr_image.dir/src/Utils/Logger.cpp.o"
	g++-8  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/run_sr_image.dir/src/Utils/Logger.cpp.o -c /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Utils/Logger.cpp

CMakeFiles/run_sr_image.dir/src/Utils/Logger.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/run_sr_image.dir/src/Utils/Logger.cpp.i"
	g++-8 $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Utils/Logger.cpp > CMakeFiles/run_sr_image.dir/src/Utils/Logger.cpp.i

CMakeFiles/run_sr_image.dir/src/Utils/Logger.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/run_sr_image.dir/src/Utils/Logger.cpp.s"
	g++-8 $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Utils/Logger.cpp -o CMakeFiles/run_sr_image.dir/src/Utils/Logger.cpp.s

CMakeFiles/run_sr_image.dir/src/Utils/Logger.cpp.o.requires:

.PHONY : CMakeFiles/run_sr_image.dir/src/Utils/Logger.cpp.o.requires

CMakeFiles/run_sr_image.dir/src/Utils/Logger.cpp.o.provides: CMakeFiles/run_sr_image.dir/src/Utils/Logger.cpp.o.requires
	$(MAKE) -f CMakeFiles/run_sr_image.dir/build.make CMakeFiles/run_sr_image.dir/src/Utils/Logger.cpp.o.provides.build
.PHONY : CMakeFiles/run_sr_image.dir/src/Utils/Logger.cpp.o.provides

CMakeFiles/run_sr_image.dir/src/Utils/Logger.cpp.o.provides.build: CMakeFiles/run_sr_image.dir/src/Utils/Logger.cpp.o


CMakeFiles/run_sr_image.dir/src/Utils/LogUtils.cpp.o: CMakeFiles/run_sr_image.dir/flags.make
CMakeFiles/run_sr_image.dir/src/Utils/LogUtils.cpp.o: ../src/Utils/LogUtils.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building CXX object CMakeFiles/run_sr_image.dir/src/Utils/LogUtils.cpp.o"
	g++-8  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/run_sr_image.dir/src/Utils/LogUtils.cpp.o -c /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Utils/LogUtils.cpp

CMakeFiles/run_sr_image.dir/src/Utils/LogUtils.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/run_sr_image.dir/src/Utils/LogUtils.cpp.i"
	g++-8 $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Utils/LogUtils.cpp > CMakeFiles/run_sr_image.dir/src/Utils/LogUtils.cpp.i

CMakeFiles/run_sr_image.dir/src/Utils/LogUtils.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/run_sr_image.dir/src/Utils/LogUtils.cpp.s"
	g++-8 $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Utils/LogUtils.cpp -o CMakeFiles/run_sr_image.dir/src/Utils/LogUtils.cpp.s

CMakeFiles/run_sr_image.dir/src/Utils/LogUtils.cpp.o.requires:

.PHONY : CMakeFiles/run_sr_image.dir/src/Utils/LogUtils.cpp.o.requires

CMakeFiles/run_sr_image.dir/src/Utils/LogUtils.cpp.o.provides: CMakeFiles/run_sr_image.dir/src/Utils/LogUtils.cpp.o.requires
	$(MAKE) -f CMakeFiles/run_sr_image.dir/build.make CMakeFiles/run_sr_image.dir/src/Utils/LogUtils.cpp.o.provides.build
.PHONY : CMakeFiles/run_sr_image.dir/src/Utils/LogUtils.cpp.o.provides

CMakeFiles/run_sr_image.dir/src/Utils/LogUtils.cpp.o.provides.build: CMakeFiles/run_sr_image.dir/src/Utils/LogUtils.cpp.o


CMakeFiles/run_sr_image.dir/run_sr_image_autogen/mocs_compilation.cpp.o: CMakeFiles/run_sr_image.dir/flags.make
CMakeFiles/run_sr_image.dir/run_sr_image_autogen/mocs_compilation.cpp.o: run_sr_image_autogen/mocs_compilation.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building CXX object CMakeFiles/run_sr_image.dir/run_sr_image_autogen/mocs_compilation.cpp.o"
	g++-8  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/run_sr_image.dir/run_sr_image_autogen/mocs_compilation.cpp.o -c /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build/run_sr_image_autogen/mocs_compilation.cpp

CMakeFiles/run_sr_image.dir/run_sr_image_autogen/mocs_compilation.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/run_sr_image.dir/run_sr_image_autogen/mocs_compilation.cpp.i"
	g++-8 $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build/run_sr_image_autogen/mocs_compilation.cpp > CMakeFiles/run_sr_image.dir/run_sr_image_autogen/mocs_compilation.cpp.i

CMakeFiles/run_sr_image.dir/run_sr_image_autogen/mocs_compilation.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/run_sr_image.dir/run_sr_image_autogen/mocs_compilation.cpp.s"
	g++-8 $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build/run_sr_image_autogen/mocs_compilation.cpp -o CMakeFiles/run_sr_image.dir/run_sr_image_autogen/mocs_compilation.cpp.s

CMakeFiles/run_sr_image.dir/run_sr_image_autogen/mocs_compilation.cpp.o.requires:

.PHONY : CMakeFiles/run_sr_image.dir/run_sr_image_autogen/mocs_compilation.cpp.o.requires

CMakeFiles/run_sr_image.dir/run_sr_image_autogen/mocs_compilation.cpp.o.provides: CMakeFiles/run_sr_image.dir/run_sr_image_autogen/mocs_compilation.cpp.o.requires
	$(MAKE) -f CMakeFiles/run_sr_image.dir/build.make CMakeFiles/run_sr_image.dir/run_sr_image_autogen/mocs_compilation.cpp.o.provides.build
.PHONY : CMakeFiles/run_sr_image.dir/run_sr_image_autogen/mocs_compilation.cpp.o.provides

CMakeFiles/run_sr_image.dir/run_sr_image_autogen/mocs_compilation.cpp.o.provides.build: CMakeFiles/run_sr_image.dir/run_sr_image_autogen/mocs_compilation.cpp.o


# Object files for target run_sr_image
run_sr_image_OBJECTS = \
"CMakeFiles/run_sr_image.dir/src/tools/run_sr_image.cpp.o" \
"CMakeFiles/run_sr_image.dir/src/Processing/SuperResolution.cpp.o" \
"CMakeFiles/run_sr_image.dir/src/SuperEigen/src/SuperResEngine.cpp.o" \
"CMakeFiles/run_sr_image.dir/src/SuperEigen/src/ModelSession.cpp.o" \
"CMakeFiles/run_sr_image.dir/src/SuperEigen/src/PrePostProcessor.cpp.o" \
"CMakeFiles/run_sr_image.dir/src/SuperEigen/src/SuperResConfig.cpp.o" \
"CMakeFiles/run_sr_image.dir/src/Utils/Logger.cpp.o" \
"CMakeFiles/run_sr_image.dir/src/Utils/LogUtils.cpp.o" \
"CMakeFiles/run_sr_image.dir/run_sr_image_autogen/mocs_compilation.cpp.o"

# External object files for target run_sr_image
run_sr_image_EXTERNAL_OBJECTS =

bin/run_sr_image: CMakeFiles/run_sr_image.dir/src/tools/run_sr_image.cpp.o
bin/run_sr_image: CMakeFiles/run_sr_image.dir/src/Processing/SuperResolution.cpp.o
bin/run_sr_image: CMakeFiles/run_sr_image.dir/src/SuperEigen/src/SuperResEngine.cpp.o
bin/run_sr_image: CMakeFiles/run_sr_image.dir/src/SuperEigen/src/ModelSession.cpp.o
bin/run_sr_image: CMakeFiles/run_sr_image.dir/src/SuperEigen/src/PrePostProcessor.cpp.o
bin/run_sr_image: CMakeFiles/run_sr_image.dir/src/SuperEigen/src/SuperResConfig.cpp.o
bin/run_sr_image: CMakeFiles/run_sr_image.dir/src/Utils/Logger.cpp.o
bin/run_sr_image: CMakeFiles/run_sr_image.dir/src/Utils/LogUtils.cpp.o
bin/run_sr_image: CMakeFiles/run_sr_image.dir/run_sr_image_autogen/mocs_compilation.cpp.o
bin/run_sr_image: CMakeFiles/run_sr_image.dir/build.make
bin/run_sr_image: /usr/lib/x86_64-linux-gnu/libopencv_shape.so.3.2.0
bin/run_sr_image: /usr/lib/x86_64-linux-gnu/libopencv_stitching.so.3.2.0
bin/run_sr_image: /usr/lib/x86_64-linux-gnu/libopencv_superres.so.3.2.0
bin/run_sr_image: /usr/lib/x86_64-linux-gnu/libopencv_videostab.so.3.2.0
bin/run_sr_image: /usr/lib/x86_64-linux-gnu/libopencv_aruco.so.3.2.0
bin/run_sr_image: /usr/lib/x86_64-linux-gnu/libopencv_bgsegm.so.3.2.0
bin/run_sr_image: /usr/lib/x86_64-linux-gnu/libopencv_bioinspired.so.3.2.0
bin/run_sr_image: /usr/lib/x86_64-linux-gnu/libopencv_ccalib.so.3.2.0
bin/run_sr_image: /usr/lib/x86_64-linux-gnu/libopencv_datasets.so.3.2.0
bin/run_sr_image: /usr/lib/x86_64-linux-gnu/libopencv_dpm.so.3.2.0
bin/run_sr_image: /usr/lib/x86_64-linux-gnu/libopencv_face.so.3.2.0
bin/run_sr_image: /usr/lib/x86_64-linux-gnu/libopencv_freetype.so.3.2.0
bin/run_sr_image: /usr/lib/x86_64-linux-gnu/libopencv_fuzzy.so.3.2.0
bin/run_sr_image: /usr/lib/x86_64-linux-gnu/libopencv_hdf.so.3.2.0
bin/run_sr_image: /usr/lib/x86_64-linux-gnu/libopencv_line_descriptor.so.3.2.0
bin/run_sr_image: /usr/lib/x86_64-linux-gnu/libopencv_optflow.so.3.2.0
bin/run_sr_image: /usr/lib/x86_64-linux-gnu/libopencv_plot.so.3.2.0
bin/run_sr_image: /usr/lib/x86_64-linux-gnu/libopencv_reg.so.3.2.0
bin/run_sr_image: /usr/lib/x86_64-linux-gnu/libopencv_saliency.so.3.2.0
bin/run_sr_image: /usr/lib/x86_64-linux-gnu/libopencv_stereo.so.3.2.0
bin/run_sr_image: /usr/lib/x86_64-linux-gnu/libopencv_structured_light.so.3.2.0
bin/run_sr_image: /usr/lib/x86_64-linux-gnu/libopencv_surface_matching.so.3.2.0
bin/run_sr_image: /usr/lib/x86_64-linux-gnu/libopencv_text.so.3.2.0
bin/run_sr_image: /usr/lib/x86_64-linux-gnu/libopencv_ximgproc.so.3.2.0
bin/run_sr_image: /usr/lib/x86_64-linux-gnu/libopencv_xobjdetect.so.3.2.0
bin/run_sr_image: /usr/lib/x86_64-linux-gnu/libopencv_xphoto.so.3.2.0
bin/run_sr_image: ../onnx/onnxruntime-linux-x64-1.15.1/lib/libonnxruntime.so
bin/run_sr_image: /usr/lib/x86_64-linux-gnu/libopencv_video.so.3.2.0
bin/run_sr_image: /usr/lib/x86_64-linux-gnu/libopencv_viz.so.3.2.0
bin/run_sr_image: /usr/lib/x86_64-linux-gnu/libopencv_phase_unwrapping.so.3.2.0
bin/run_sr_image: /usr/lib/x86_64-linux-gnu/libopencv_rgbd.so.3.2.0
bin/run_sr_image: /usr/lib/x86_64-linux-gnu/libopencv_calib3d.so.3.2.0
bin/run_sr_image: /usr/lib/x86_64-linux-gnu/libopencv_features2d.so.3.2.0
bin/run_sr_image: /usr/lib/x86_64-linux-gnu/libopencv_flann.so.3.2.0
bin/run_sr_image: /usr/lib/x86_64-linux-gnu/libopencv_objdetect.so.3.2.0
bin/run_sr_image: /usr/lib/x86_64-linux-gnu/libopencv_ml.so.3.2.0
bin/run_sr_image: /usr/lib/x86_64-linux-gnu/libopencv_highgui.so.3.2.0
bin/run_sr_image: /usr/lib/x86_64-linux-gnu/libopencv_photo.so.3.2.0
bin/run_sr_image: /usr/lib/x86_64-linux-gnu/libopencv_videoio.so.3.2.0
bin/run_sr_image: /usr/lib/x86_64-linux-gnu/libopencv_imgcodecs.so.3.2.0
bin/run_sr_image: /usr/lib/x86_64-linux-gnu/libopencv_imgproc.so.3.2.0
bin/run_sr_image: /usr/lib/x86_64-linux-gnu/libopencv_core.so.3.2.0
bin/run_sr_image: CMakeFiles/run_sr_image.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Linking CXX executable bin/run_sr_image"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/run_sr_image.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/run_sr_image.dir/build: bin/run_sr_image

.PHONY : CMakeFiles/run_sr_image.dir/build

CMakeFiles/run_sr_image.dir/requires: CMakeFiles/run_sr_image.dir/src/tools/run_sr_image.cpp.o.requires
CMakeFiles/run_sr_image.dir/requires: CMakeFiles/run_sr_image.dir/src/Processing/SuperResolution.cpp.o.requires
CMakeFiles/run_sr_image.dir/requires: CMakeFiles/run_sr_image.dir/src/SuperEigen/src/SuperResEngine.cpp.o.requires
CMakeFiles/run_sr_image.dir/requires: CMakeFiles/run_sr_image.dir/src/SuperEigen/src/ModelSession.cpp.o.requires
CMakeFiles/run_sr_image.dir/requires: CMakeFiles/run_sr_image.dir/src/SuperEigen/src/PrePostProcessor.cpp.o.requires
CMakeFiles/run_sr_image.dir/requires: CMakeFiles/run_sr_image.dir/src/SuperEigen/src/SuperResConfig.cpp.o.requires
CMakeFiles/run_sr_image.dir/requires: CMakeFiles/run_sr_image.dir/src/Utils/Logger.cpp.o.requires
CMakeFiles/run_sr_image.dir/requires: CMakeFiles/run_sr_image.dir/src/Utils/LogUtils.cpp.o.requires
CMakeFiles/run_sr_image.dir/requires: CMakeFiles/run_sr_image.dir/run_sr_image_autogen/mocs_compilation.cpp.o.requires

.PHONY : CMakeFiles/run_sr_image.dir/requires

CMakeFiles/run_sr_image.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/run_sr_image.dir/cmake_clean.cmake
.PHONY : CMakeFiles/run_sr_image.dir/clean

CMakeFiles/run_sr_image.dir/depend:
	cd /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build/CMakeFiles/run_sr_image.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/run_sr_image.dir/depend

