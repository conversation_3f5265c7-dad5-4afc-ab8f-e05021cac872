# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.10

CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o
 ../onnx/onnxruntime-linux-x64-1.15.1/include/onnxruntime_c_api.h
 ../onnx/onnxruntime-linux-x64-1.15.1/include/onnxruntime_cxx_api.h
 ../onnx/onnxruntime-linux-x64-1.15.1/include/onnxruntime_cxx_inline.h
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build/VideoSRLiteGUI_autogen/HC4UDM2PON/moc_WorkerPool.cpp
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build/VideoSRLiteGUI_autogen/WAYUIA5GRM/moc_FileListWidget.cpp
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build/VideoSRLiteGUI_autogen/WAYUIA5GRM/moc_ImagePreviewWidget.cpp
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build/VideoSRLiteGUI_autogen/WAYUIA5GRM/moc_ImageProcessor.cpp
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build/VideoSRLiteGUI_autogen/WAYUIA5GRM/moc_MainWindow.cpp
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build/VideoSRLiteGUI_autogen/WAYUIA5GRM/moc_SettingsPanel.cpp
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build/VideoSRLiteGUI_autogen/mocs_compilation.cpp
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/DataStruct/FrameData.h
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/SuperEigen/include/ModelSession.h
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/SuperEigen/include/PrePostProcessor.h
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/SuperEigen/include/SuperResConfig.h
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/SuperEigen/include/SuperResEngine.h
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/UI/FileListWidget.h
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/UI/ImagePreviewWidget.h
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/UI/ImageProcessor.h
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/UI/MainWindow.h
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/UI/SettingsPanel.h
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/WorkerPool/WorkerPool.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QAtomicInt
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QDateTime
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QElapsedTimer
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QFileInfo
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QMap
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QMimeData
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QMutex
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QObject
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QQueue
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QRunnable
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QSize
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QStandardPaths
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QStringList
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QThread
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QThreadPool
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QTimer
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QUrl
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QWaitCondition
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qabstractitemmodel.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qalgorithms.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qarraydata.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qatomic.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qatomic_bootstrap.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qatomic_cxx11.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qatomic_msvc.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qbasicatomic.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qbasictimer.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qbytearray.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qbytearraylist.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qchar.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qcompilerdetection.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qconfig-bootstrapped.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qconfig.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qcontainerfwd.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qcontiguouscache.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qcoreapplication.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qcoreevent.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qdatastream.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qdatetime.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qdebug.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qdir.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qelapsedtimer.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qeventloop.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qfile.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qfiledevice.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qfileinfo.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qflags.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qgenericatomic.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qglobal.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qglobalstatic.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qhash.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qhashfunctions.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qiodevice.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qitemselectionmodel.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qiterator.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qline.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qlist.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qlocale.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qlogging.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qmap.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qmargins.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qmetatype.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qmimedata.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qmutex.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qnamespace.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qnumeric.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qobject.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qobject_impl.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qobjectdefs.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qobjectdefs_impl.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qpair.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qpoint.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qprocessordetection.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qqueue.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qrect.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qrefcount.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qregexp.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qregularexpression.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qrunnable.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qscopedpointer.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qset.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qshareddata.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qsharedpointer.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qsharedpointer_impl.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qsize.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qstandardpaths.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qstring.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qstringbuilder.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qstringlist.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qstringmatcher.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qsysinfo.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qsystemdetection.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qtcore-config.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qtextstream.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qthread.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qthreadpool.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qtimer.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qtypeinfo.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qurl.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qurlquery.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qvariant.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qvarlengtharray.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qvector.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qversiontagging.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qwaitcondition.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/QCloseEvent
 /usr/include/x86_64-linux-gnu/qt5/QtGui/QDragEnterEvent
 /usr/include/x86_64-linux-gnu/qt5/QtGui/QDropEvent
 /usr/include/x86_64-linux-gnu/qt5/QtGui/QPixmap
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qbrush.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qcolor.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qcursor.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qevent.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qfont.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qfontinfo.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qfontmetrics.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qguiapplication.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qicon.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qimage.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qinputmethod.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qkeysequence.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qmatrix.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qpaintdevice.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qpainterpath.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qpalette.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qpen.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qpixelformat.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qpixmap.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qpolygon.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qregion.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qrgb.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qrgba64.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qtextcursor.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qtextdocument.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qtextformat.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qtextoption.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qtgui-config.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qtguiglobal.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qtouchdevice.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qtransform.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qvalidator.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qvector2d.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qwindowdefs.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qwindowdefs_win.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QApplication
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QCheckBox
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QComboBox
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QDoubleSpinBox
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QFileDialog
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QGridLayout
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QGroupBox
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QHBoxLayout
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QLabel
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QLineEdit
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QListWidget
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QListWidgetItem
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QMainWindow
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QMessageBox
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QProgressBar
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QPushButton
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QScrollArea
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QScrollBar
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QSlider
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QSpinBox
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QSplitter
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QStackedWidget
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QTextEdit
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QVBoxLayout
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QWidget
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qabstractbutton.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qabstractitemdelegate.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qabstractitemview.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qabstractscrollarea.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qabstractslider.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qabstractspinbox.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qapplication.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qboxlayout.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qcheckbox.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qcombobox.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qdesktopwidget.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qdialog.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qfiledialog.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qframe.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qgridlayout.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qgroupbox.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qlabel.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qlayout.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qlayoutitem.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qlineedit.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qlistview.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qlistwidget.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qmainwindow.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qmessagebox.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qprogressbar.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qpushbutton.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qrubberband.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qscrollarea.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qscrollbar.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qsizepolicy.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qslider.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qspinbox.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qsplitter.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qstackedwidget.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qstyle.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qstyleoption.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qtabbar.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qtabwidget.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qtextedit.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qtwidgets-config.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qtwidgetsglobal.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qwidget.h
CMakeFiles/VideoSRLiteGUI.dir/src/AppController/AppController.cpp.o
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/AppController/AppController.cpp
CMakeFiles/VideoSRLiteGUI.dir/src/AppController/WorkerPool.cpp.o
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/AppController/WorkerPool.cpp
CMakeFiles/VideoSRLiteGUI.dir/src/AudioProc/AudioProc.cpp.o
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/AudioProc/AudioProc.cpp
CMakeFiles/VideoSRLiteGUI.dir/src/AudioProcessor/AudioProcessor.cpp.o
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/AudioProcessor/AudioProcessor.cpp
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/AudioDecoder.cpp.o
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/DataStruct/AudioFrameData.h
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Decoder/include/AudioDecoder.h
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Decoder/src/AudioDecoder.cpp
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Utils/Logger.h
 /usr/include/x86_64-linux-gnu/libavcodec/avcodec.h
 /usr/include/x86_64-linux-gnu/libavcodec/bsf.h
 /usr/include/x86_64-linux-gnu/libavcodec/codec.h
 /usr/include/x86_64-linux-gnu/libavcodec/codec_desc.h
 /usr/include/x86_64-linux-gnu/libavcodec/codec_id.h
 /usr/include/x86_64-linux-gnu/libavcodec/codec_par.h
 /usr/include/x86_64-linux-gnu/libavcodec/packet.h
 /usr/include/x86_64-linux-gnu/libavcodec/version.h
 /usr/include/x86_64-linux-gnu/libavformat/avformat.h
 /usr/include/x86_64-linux-gnu/libavformat/avio.h
 /usr/include/x86_64-linux-gnu/libavformat/version.h
 /usr/include/x86_64-linux-gnu/libavutil/attributes.h
 /usr/include/x86_64-linux-gnu/libavutil/avconfig.h
 /usr/include/x86_64-linux-gnu/libavutil/avutil.h
 /usr/include/x86_64-linux-gnu/libavutil/buffer.h
 /usr/include/x86_64-linux-gnu/libavutil/channel_layout.h
 /usr/include/x86_64-linux-gnu/libavutil/common.h
 /usr/include/x86_64-linux-gnu/libavutil/cpu.h
 /usr/include/x86_64-linux-gnu/libavutil/dict.h
 /usr/include/x86_64-linux-gnu/libavutil/error.h
 /usr/include/x86_64-linux-gnu/libavutil/frame.h
 /usr/include/x86_64-linux-gnu/libavutil/hwcontext.h
 /usr/include/x86_64-linux-gnu/libavutil/intfloat.h
 /usr/include/x86_64-linux-gnu/libavutil/log.h
 /usr/include/x86_64-linux-gnu/libavutil/macros.h
 /usr/include/x86_64-linux-gnu/libavutil/mathematics.h
 /usr/include/x86_64-linux-gnu/libavutil/mem.h
 /usr/include/x86_64-linux-gnu/libavutil/opt.h
 /usr/include/x86_64-linux-gnu/libavutil/pixfmt.h
 /usr/include/x86_64-linux-gnu/libavutil/rational.h
 /usr/include/x86_64-linux-gnu/libavutil/samplefmt.h
 /usr/include/x86_64-linux-gnu/libavutil/version.h
 /usr/include/x86_64-linux-gnu/libswresample/swresample.h
 /usr/include/x86_64-linux-gnu/libswresample/version.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/Decoder.cpp.o
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/DataStruct/AudioFrameData.h
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/DataStruct/FrameData.h
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Decoder/include/AudioDecoder.h
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Decoder/include/Decoder.h
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Decoder/include/VideoDecoder.h
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Decoder/src/Decoder.cpp
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Utils/Logger.h
 /usr/include/x86_64-linux-gnu/libavcodec/avcodec.h
 /usr/include/x86_64-linux-gnu/libavcodec/bsf.h
 /usr/include/x86_64-linux-gnu/libavcodec/codec.h
 /usr/include/x86_64-linux-gnu/libavcodec/codec_desc.h
 /usr/include/x86_64-linux-gnu/libavcodec/codec_id.h
 /usr/include/x86_64-linux-gnu/libavcodec/codec_par.h
 /usr/include/x86_64-linux-gnu/libavcodec/packet.h
 /usr/include/x86_64-linux-gnu/libavcodec/version.h
 /usr/include/x86_64-linux-gnu/libavformat/avformat.h
 /usr/include/x86_64-linux-gnu/libavformat/avio.h
 /usr/include/x86_64-linux-gnu/libavformat/version.h
 /usr/include/x86_64-linux-gnu/libavutil/attributes.h
 /usr/include/x86_64-linux-gnu/libavutil/avconfig.h
 /usr/include/x86_64-linux-gnu/libavutil/avutil.h
 /usr/include/x86_64-linux-gnu/libavutil/buffer.h
 /usr/include/x86_64-linux-gnu/libavutil/channel_layout.h
 /usr/include/x86_64-linux-gnu/libavutil/common.h
 /usr/include/x86_64-linux-gnu/libavutil/cpu.h
 /usr/include/x86_64-linux-gnu/libavutil/dict.h
 /usr/include/x86_64-linux-gnu/libavutil/error.h
 /usr/include/x86_64-linux-gnu/libavutil/frame.h
 /usr/include/x86_64-linux-gnu/libavutil/hwcontext.h
 /usr/include/x86_64-linux-gnu/libavutil/intfloat.h
 /usr/include/x86_64-linux-gnu/libavutil/log.h
 /usr/include/x86_64-linux-gnu/libavutil/macros.h
 /usr/include/x86_64-linux-gnu/libavutil/mathematics.h
 /usr/include/x86_64-linux-gnu/libavutil/mem.h
 /usr/include/x86_64-linux-gnu/libavutil/pixdesc.h
 /usr/include/x86_64-linux-gnu/libavutil/pixfmt.h
 /usr/include/x86_64-linux-gnu/libavutil/rational.h
 /usr/include/x86_64-linux-gnu/libavutil/samplefmt.h
 /usr/include/x86_64-linux-gnu/libavutil/version.h
 /usr/include/x86_64-linux-gnu/libswresample/swresample.h
 /usr/include/x86_64-linux-gnu/libswresample/version.h
 /usr/include/x86_64-linux-gnu/libswscale/swscale.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/VideoDecoder.cpp.o
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/DataStruct/FrameData.h
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Decoder/include/VideoDecoder.h
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Decoder/src/VideoDecoder.cpp
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Utils/Logger.h
 /usr/include/x86_64-linux-gnu/libavcodec/avcodec.h
 /usr/include/x86_64-linux-gnu/libavcodec/bsf.h
 /usr/include/x86_64-linux-gnu/libavcodec/codec.h
 /usr/include/x86_64-linux-gnu/libavcodec/codec_desc.h
 /usr/include/x86_64-linux-gnu/libavcodec/codec_id.h
 /usr/include/x86_64-linux-gnu/libavcodec/codec_par.h
 /usr/include/x86_64-linux-gnu/libavcodec/packet.h
 /usr/include/x86_64-linux-gnu/libavcodec/version.h
 /usr/include/x86_64-linux-gnu/libavformat/avformat.h
 /usr/include/x86_64-linux-gnu/libavformat/avio.h
 /usr/include/x86_64-linux-gnu/libavformat/version.h
 /usr/include/x86_64-linux-gnu/libavutil/attributes.h
 /usr/include/x86_64-linux-gnu/libavutil/avconfig.h
 /usr/include/x86_64-linux-gnu/libavutil/avutil.h
 /usr/include/x86_64-linux-gnu/libavutil/buffer.h
 /usr/include/x86_64-linux-gnu/libavutil/channel_layout.h
 /usr/include/x86_64-linux-gnu/libavutil/common.h
 /usr/include/x86_64-linux-gnu/libavutil/cpu.h
 /usr/include/x86_64-linux-gnu/libavutil/dict.h
 /usr/include/x86_64-linux-gnu/libavutil/error.h
 /usr/include/x86_64-linux-gnu/libavutil/frame.h
 /usr/include/x86_64-linux-gnu/libavutil/hwcontext.h
 /usr/include/x86_64-linux-gnu/libavutil/intfloat.h
 /usr/include/x86_64-linux-gnu/libavutil/log.h
 /usr/include/x86_64-linux-gnu/libavutil/macros.h
 /usr/include/x86_64-linux-gnu/libavutil/mathematics.h
 /usr/include/x86_64-linux-gnu/libavutil/mem.h
 /usr/include/x86_64-linux-gnu/libavutil/pixdesc.h
 /usr/include/x86_64-linux-gnu/libavutil/pixfmt.h
 /usr/include/x86_64-linux-gnu/libavutil/rational.h
 /usr/include/x86_64-linux-gnu/libavutil/samplefmt.h
 /usr/include/x86_64-linux-gnu/libavutil/version.h
 /usr/include/x86_64-linux-gnu/libswscale/swscale.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/AudioEncoder.cpp.o
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/DataStruct/AudioFrameData.h
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Encoder/AudioEncoder.cpp
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Encoder/AudioEncoder.h
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Utils/Logger.h
 /usr/include/x86_64-linux-gnu/libavcodec/avcodec.h
 /usr/include/x86_64-linux-gnu/libavcodec/bsf.h
 /usr/include/x86_64-linux-gnu/libavcodec/codec.h
 /usr/include/x86_64-linux-gnu/libavcodec/codec_desc.h
 /usr/include/x86_64-linux-gnu/libavcodec/codec_id.h
 /usr/include/x86_64-linux-gnu/libavcodec/codec_par.h
 /usr/include/x86_64-linux-gnu/libavcodec/packet.h
 /usr/include/x86_64-linux-gnu/libavcodec/version.h
 /usr/include/x86_64-linux-gnu/libavformat/avformat.h
 /usr/include/x86_64-linux-gnu/libavformat/avio.h
 /usr/include/x86_64-linux-gnu/libavformat/version.h
 /usr/include/x86_64-linux-gnu/libavutil/attributes.h
 /usr/include/x86_64-linux-gnu/libavutil/avconfig.h
 /usr/include/x86_64-linux-gnu/libavutil/avutil.h
 /usr/include/x86_64-linux-gnu/libavutil/buffer.h
 /usr/include/x86_64-linux-gnu/libavutil/channel_layout.h
 /usr/include/x86_64-linux-gnu/libavutil/common.h
 /usr/include/x86_64-linux-gnu/libavutil/cpu.h
 /usr/include/x86_64-linux-gnu/libavutil/dict.h
 /usr/include/x86_64-linux-gnu/libavutil/error.h
 /usr/include/x86_64-linux-gnu/libavutil/frame.h
 /usr/include/x86_64-linux-gnu/libavutil/hwcontext.h
 /usr/include/x86_64-linux-gnu/libavutil/intfloat.h
 /usr/include/x86_64-linux-gnu/libavutil/log.h
 /usr/include/x86_64-linux-gnu/libavutil/macros.h
 /usr/include/x86_64-linux-gnu/libavutil/mathematics.h
 /usr/include/x86_64-linux-gnu/libavutil/mem.h
 /usr/include/x86_64-linux-gnu/libavutil/opt.h
 /usr/include/x86_64-linux-gnu/libavutil/pixfmt.h
 /usr/include/x86_64-linux-gnu/libavutil/rational.h
 /usr/include/x86_64-linux-gnu/libavutil/samplefmt.h
 /usr/include/x86_64-linux-gnu/libavutil/version.h
 /usr/include/x86_64-linux-gnu/libswresample/swresample.h
 /usr/include/x86_64-linux-gnu/libswresample/version.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/DataStruct/AudioFrameData.h
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/DataStruct/FrameData.h
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Encoder/AudioEncoder.h
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Encoder/Encoder.cpp
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Encoder/Encoder.h
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Encoder/Muxer.h
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Encoder/VideoEncoder.h
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Utils/Logger.h
 /usr/include/x86_64-linux-gnu/libavcodec/avcodec.h
 /usr/include/x86_64-linux-gnu/libavcodec/bsf.h
 /usr/include/x86_64-linux-gnu/libavcodec/codec.h
 /usr/include/x86_64-linux-gnu/libavcodec/codec_desc.h
 /usr/include/x86_64-linux-gnu/libavcodec/codec_id.h
 /usr/include/x86_64-linux-gnu/libavcodec/codec_par.h
 /usr/include/x86_64-linux-gnu/libavcodec/packet.h
 /usr/include/x86_64-linux-gnu/libavcodec/version.h
 /usr/include/x86_64-linux-gnu/libavformat/avformat.h
 /usr/include/x86_64-linux-gnu/libavformat/avio.h
 /usr/include/x86_64-linux-gnu/libavformat/version.h
 /usr/include/x86_64-linux-gnu/libavutil/attributes.h
 /usr/include/x86_64-linux-gnu/libavutil/avconfig.h
 /usr/include/x86_64-linux-gnu/libavutil/avutil.h
 /usr/include/x86_64-linux-gnu/libavutil/buffer.h
 /usr/include/x86_64-linux-gnu/libavutil/channel_layout.h
 /usr/include/x86_64-linux-gnu/libavutil/common.h
 /usr/include/x86_64-linux-gnu/libavutil/cpu.h
 /usr/include/x86_64-linux-gnu/libavutil/dict.h
 /usr/include/x86_64-linux-gnu/libavutil/error.h
 /usr/include/x86_64-linux-gnu/libavutil/frame.h
 /usr/include/x86_64-linux-gnu/libavutil/hwcontext.h
 /usr/include/x86_64-linux-gnu/libavutil/imgutils.h
 /usr/include/x86_64-linux-gnu/libavutil/intfloat.h
 /usr/include/x86_64-linux-gnu/libavutil/log.h
 /usr/include/x86_64-linux-gnu/libavutil/macros.h
 /usr/include/x86_64-linux-gnu/libavutil/mathematics.h
 /usr/include/x86_64-linux-gnu/libavutil/mem.h
 /usr/include/x86_64-linux-gnu/libavutil/opt.h
 /usr/include/x86_64-linux-gnu/libavutil/pixdesc.h
 /usr/include/x86_64-linux-gnu/libavutil/pixfmt.h
 /usr/include/x86_64-linux-gnu/libavutil/rational.h
 /usr/include/x86_64-linux-gnu/libavutil/samplefmt.h
 /usr/include/x86_64-linux-gnu/libavutil/version.h
 /usr/include/x86_64-linux-gnu/libswresample/swresample.h
 /usr/include/x86_64-linux-gnu/libswresample/version.h
 /usr/include/x86_64-linux-gnu/libswscale/swscale.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Muxer.cpp.o
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Encoder/Muxer.cpp
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Encoder/Muxer.h
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Utils/Logger.h
 /usr/include/x86_64-linux-gnu/libavcodec/avcodec.h
 /usr/include/x86_64-linux-gnu/libavcodec/bsf.h
 /usr/include/x86_64-linux-gnu/libavcodec/codec.h
 /usr/include/x86_64-linux-gnu/libavcodec/codec_desc.h
 /usr/include/x86_64-linux-gnu/libavcodec/codec_id.h
 /usr/include/x86_64-linux-gnu/libavcodec/codec_par.h
 /usr/include/x86_64-linux-gnu/libavcodec/packet.h
 /usr/include/x86_64-linux-gnu/libavcodec/version.h
 /usr/include/x86_64-linux-gnu/libavformat/avformat.h
 /usr/include/x86_64-linux-gnu/libavformat/avio.h
 /usr/include/x86_64-linux-gnu/libavformat/version.h
 /usr/include/x86_64-linux-gnu/libavutil/attributes.h
 /usr/include/x86_64-linux-gnu/libavutil/avconfig.h
 /usr/include/x86_64-linux-gnu/libavutil/avutil.h
 /usr/include/x86_64-linux-gnu/libavutil/buffer.h
 /usr/include/x86_64-linux-gnu/libavutil/channel_layout.h
 /usr/include/x86_64-linux-gnu/libavutil/common.h
 /usr/include/x86_64-linux-gnu/libavutil/cpu.h
 /usr/include/x86_64-linux-gnu/libavutil/dict.h
 /usr/include/x86_64-linux-gnu/libavutil/error.h
 /usr/include/x86_64-linux-gnu/libavutil/frame.h
 /usr/include/x86_64-linux-gnu/libavutil/hwcontext.h
 /usr/include/x86_64-linux-gnu/libavutil/intfloat.h
 /usr/include/x86_64-linux-gnu/libavutil/log.h
 /usr/include/x86_64-linux-gnu/libavutil/macros.h
 /usr/include/x86_64-linux-gnu/libavutil/mathematics.h
 /usr/include/x86_64-linux-gnu/libavutil/mem.h
 /usr/include/x86_64-linux-gnu/libavutil/pixfmt.h
 /usr/include/x86_64-linux-gnu/libavutil/rational.h
 /usr/include/x86_64-linux-gnu/libavutil/samplefmt.h
 /usr/include/x86_64-linux-gnu/libavutil/version.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/VideoEncoder.cpp.o
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/DataStruct/FrameData.h
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Encoder/VideoEncoder.cpp
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Encoder/VideoEncoder.h
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Utils/Logger.h
 /usr/include/x86_64-linux-gnu/libavcodec/avcodec.h
 /usr/include/x86_64-linux-gnu/libavcodec/bsf.h
 /usr/include/x86_64-linux-gnu/libavcodec/codec.h
 /usr/include/x86_64-linux-gnu/libavcodec/codec_desc.h
 /usr/include/x86_64-linux-gnu/libavcodec/codec_id.h
 /usr/include/x86_64-linux-gnu/libavcodec/codec_par.h
 /usr/include/x86_64-linux-gnu/libavcodec/packet.h
 /usr/include/x86_64-linux-gnu/libavcodec/version.h
 /usr/include/x86_64-linux-gnu/libavformat/avformat.h
 /usr/include/x86_64-linux-gnu/libavformat/avio.h
 /usr/include/x86_64-linux-gnu/libavformat/version.h
 /usr/include/x86_64-linux-gnu/libavutil/attributes.h
 /usr/include/x86_64-linux-gnu/libavutil/avconfig.h
 /usr/include/x86_64-linux-gnu/libavutil/avutil.h
 /usr/include/x86_64-linux-gnu/libavutil/buffer.h
 /usr/include/x86_64-linux-gnu/libavutil/channel_layout.h
 /usr/include/x86_64-linux-gnu/libavutil/common.h
 /usr/include/x86_64-linux-gnu/libavutil/cpu.h
 /usr/include/x86_64-linux-gnu/libavutil/dict.h
 /usr/include/x86_64-linux-gnu/libavutil/error.h
 /usr/include/x86_64-linux-gnu/libavutil/frame.h
 /usr/include/x86_64-linux-gnu/libavutil/hwcontext.h
 /usr/include/x86_64-linux-gnu/libavutil/imgutils.h
 /usr/include/x86_64-linux-gnu/libavutil/intfloat.h
 /usr/include/x86_64-linux-gnu/libavutil/log.h
 /usr/include/x86_64-linux-gnu/libavutil/macros.h
 /usr/include/x86_64-linux-gnu/libavutil/mathematics.h
 /usr/include/x86_64-linux-gnu/libavutil/mem.h
 /usr/include/x86_64-linux-gnu/libavutil/opt.h
 /usr/include/x86_64-linux-gnu/libavutil/pixdesc.h
 /usr/include/x86_64-linux-gnu/libavutil/pixfmt.h
 /usr/include/x86_64-linux-gnu/libavutil/rational.h
 /usr/include/x86_64-linux-gnu/libavutil/samplefmt.h
 /usr/include/x86_64-linux-gnu/libavutil/version.h
 /usr/include/x86_64-linux-gnu/libswscale/swscale.h
CMakeFiles/VideoSRLiteGUI.dir/src/PostFilter/PostFilter.cpp.o
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/PostFilter/PostFilter.cpp
CMakeFiles/VideoSRLiteGUI.dir/src/PostFilter/PostFilterProcessor.cpp.o
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/PostFilter/PostFilterProcessor.cpp
CMakeFiles/VideoSRLiteGUI.dir/src/Processing/AudioDenoiser.cpp.o
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Processing/AudioDenoiser.cpp
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Processing/AudioDenoiser.h
CMakeFiles/VideoSRLiteGUI.dir/src/Processing/PostProcessor.cpp.o
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Processing/PostProcessor.cpp
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Processing/PostProcessor.h
CMakeFiles/VideoSRLiteGUI.dir/src/Processing/SuperResolution.cpp.o
 ../onnx/onnxruntime-linux-x64-1.15.1/include/onnxruntime_c_api.h
 ../onnx/onnxruntime-linux-x64-1.15.1/include/onnxruntime_cxx_api.h
 ../onnx/onnxruntime-linux-x64-1.15.1/include/onnxruntime_cxx_inline.h
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/DataStruct/FrameData.h
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Processing/SuperResolution.cpp
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Processing/SuperResolution.h
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/SuperEigen/include/ModelSession.h
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/SuperEigen/include/PrePostProcessor.h
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/SuperEigen/include/SuperResConfig.h
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/SuperEigen/include/SuperResEngine.h
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Utils/Logger.h
CMakeFiles/VideoSRLiteGUI.dir/src/SuperEigen/src/ModelSession.cpp.o
 ../onnx/onnxruntime-linux-x64-1.15.1/include/onnxruntime_c_api.h
 ../onnx/onnxruntime-linux-x64-1.15.1/include/onnxruntime_cxx_api.h
 ../onnx/onnxruntime-linux-x64-1.15.1/include/onnxruntime_cxx_inline.h
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/SuperEigen/include/ModelSession.h
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/SuperEigen/include/SuperResConfig.h
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/SuperEigen/src/ModelSession.cpp
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Utils/Logger.h
CMakeFiles/VideoSRLiteGUI.dir/src/SuperEigen/src/PrePostProcessor.cpp.o
 ../onnx/onnxruntime-linux-x64-1.15.1/include/onnxruntime_c_api.h
 ../onnx/onnxruntime-linux-x64-1.15.1/include/onnxruntime_cxx_api.h
 ../onnx/onnxruntime-linux-x64-1.15.1/include/onnxruntime_cxx_inline.h
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/SuperEigen/include/PrePostProcessor.h
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/SuperEigen/include/SuperResConfig.h
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/SuperEigen/src/PrePostProcessor.cpp
CMakeFiles/VideoSRLiteGUI.dir/src/SuperEigen/src/SuperResConfig.cpp.o
 ../onnx/onnxruntime-linux-x64-1.15.1/include/onnxruntime_c_api.h
 ../onnx/onnxruntime-linux-x64-1.15.1/include/onnxruntime_cxx_api.h
 ../onnx/onnxruntime-linux-x64-1.15.1/include/onnxruntime_cxx_inline.h
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/SuperEigen/include/SuperResConfig.h
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/SuperEigen/src/SuperResConfig.cpp
CMakeFiles/VideoSRLiteGUI.dir/src/SuperEigen/src/SuperResEngine.cpp.o
 ../onnx/onnxruntime-linux-x64-1.15.1/include/onnxruntime_c_api.h
 ../onnx/onnxruntime-linux-x64-1.15.1/include/onnxruntime_cxx_api.h
 ../onnx/onnxruntime-linux-x64-1.15.1/include/onnxruntime_cxx_inline.h
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/DataStruct/FrameData.h
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/SuperEigen/include/ModelSession.h
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/SuperEigen/include/PrePostProcessor.h
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/SuperEigen/include/SuperResConfig.h
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/SuperEigen/include/SuperResEngine.h
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/SuperEigen/src/SuperResEngine.cpp
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Utils/Logger.h
CMakeFiles/VideoSRLiteGUI.dir/src/SyncVA/AVSyncManager.cpp.o
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/DataStruct/AudioFrameData.h
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/DataStruct/FrameData.h
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/SyncVA/AVSyncManager.cpp
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/SyncVA/AVSyncManager.h
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Utils/Logger.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/UI/FileListWidget.cpp
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/UI/FileListWidget.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QDateTime
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QDebug
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QFileInfo
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QMimeData
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QMimeDatabase
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QStandardPaths
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QUrl
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qabstractitemmodel.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qalgorithms.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qarraydata.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qatomic.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qatomic_bootstrap.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qatomic_cxx11.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qatomic_msvc.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qbasicatomic.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qbytearray.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qbytearraylist.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qchar.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qcompilerdetection.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qconfig-bootstrapped.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qconfig.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qcontainerfwd.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qcontiguouscache.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qcoreapplication.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qcoreevent.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qdatastream.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qdatetime.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qdebug.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qdir.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qeventloop.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qfile.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qfiledevice.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qfileinfo.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qflags.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qgenericatomic.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qglobal.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qglobalstatic.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qhash.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qhashfunctions.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qiodevice.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qitemselectionmodel.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qiterator.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qline.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qlist.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qlocale.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qlogging.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qmap.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qmargins.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qmetatype.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qmimedata.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qmimedatabase.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qmimetype.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qmutex.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qnamespace.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qnumeric.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qobject.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qobject_impl.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qobjectdefs.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qobjectdefs_impl.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qpair.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qpoint.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qprocessordetection.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qrect.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qrefcount.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qregexp.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qregularexpression.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qscopedpointer.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qset.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qshareddata.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qsharedpointer.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qsharedpointer_impl.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qsize.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qstandardpaths.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qstring.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qstringbuilder.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qstringlist.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qstringmatcher.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qsysinfo.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qsystemdetection.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qtcore-config.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qtextstream.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qtypeinfo.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qurl.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qurlquery.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qvariant.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qvarlengtharray.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qvector.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qversiontagging.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/QDragEnterEvent
 /usr/include/x86_64-linux-gnu/qt5/QtGui/QDropEvent
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qbrush.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qcolor.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qcursor.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qevent.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qfont.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qfontinfo.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qfontmetrics.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qguiapplication.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qicon.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qimage.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qinputmethod.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qkeysequence.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qmatrix.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qpaintdevice.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qpainterpath.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qpalette.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qpixelformat.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qpixmap.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qpolygon.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qregion.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qrgb.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qrgba64.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qtgui-config.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qtguiglobal.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qtouchdevice.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qtransform.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qvalidator.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qvector2d.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qwindowdefs.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qwindowdefs_win.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QApplication
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QFileDialog
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QGroupBox
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QHBoxLayout
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QLabel
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QListWidget
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QListWidgetItem
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QMessageBox
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QProgressBar
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QPushButton
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QVBoxLayout
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QWidget
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qabstractbutton.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qabstractitemdelegate.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qabstractitemview.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qabstractscrollarea.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qabstractslider.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qabstractspinbox.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qapplication.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qboxlayout.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qdesktopwidget.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qdialog.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qfiledialog.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qframe.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qgridlayout.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qgroupbox.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qlabel.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qlayout.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qlayoutitem.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qlistview.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qlistwidget.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qmessagebox.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qprogressbar.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qpushbutton.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qrubberband.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qsizepolicy.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qslider.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qstyle.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qstyleoption.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qtabbar.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qtabwidget.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qtwidgets-config.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qtwidgetsglobal.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qwidget.h
 VideoSRLiteGUI_autogen/include/FileListWidget.moc
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/UI/ImagePreviewWidget.cpp
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/UI/ImagePreviewWidget.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QDateTime
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QDebug
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QSize
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QStandardPaths
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QTimer
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qabstractitemmodel.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qalgorithms.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qarraydata.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qatomic.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qatomic_bootstrap.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qatomic_cxx11.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qatomic_msvc.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qbasicatomic.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qbasictimer.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qbytearray.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qbytearraylist.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qchar.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qcompilerdetection.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qconfig-bootstrapped.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qconfig.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qcontainerfwd.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qcontiguouscache.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qcoreevent.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qdatastream.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qdatetime.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qdebug.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qdir.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qfile.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qfiledevice.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qfileinfo.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qflags.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qgenericatomic.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qglobal.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qglobalstatic.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qhash.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qhashfunctions.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qiodevice.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qiterator.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qline.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qlist.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qlocale.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qlogging.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qmap.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qmargins.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qmetatype.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qmutex.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qnamespace.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qnumeric.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qobject.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qobject_impl.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qobjectdefs.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qobjectdefs_impl.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qpair.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qpoint.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qprocessordetection.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qrect.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qrefcount.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qregexp.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qregularexpression.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qscopedpointer.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qset.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qshareddata.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qsharedpointer.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qsharedpointer_impl.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qsize.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qstandardpaths.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qstring.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qstringbuilder.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qstringlist.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qstringmatcher.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qsysinfo.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qsystemdetection.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qtcore-config.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qtextstream.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qtimer.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qtypeinfo.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qurl.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qurlquery.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qvariant.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qvarlengtharray.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qvector.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qversiontagging.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/QPixmap
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qbrush.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qcolor.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qcursor.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qevent.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qfont.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qfontinfo.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qfontmetrics.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qicon.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qimage.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qkeysequence.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qmatrix.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qpaintdevice.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qpainterpath.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qpalette.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qpixelformat.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qpixmap.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qpolygon.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qregion.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qrgb.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qrgba64.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qtgui-config.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qtguiglobal.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qtouchdevice.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qtransform.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qvalidator.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qvector2d.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qwindowdefs.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qwindowdefs_win.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QComboBox
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QFileDialog
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QGroupBox
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QHBoxLayout
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QLabel
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QMessageBox
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QPushButton
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QScrollArea
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QScrollBar
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QStackedWidget
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QVBoxLayout
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QWidget
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qabstractbutton.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qabstractitemdelegate.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qabstractscrollarea.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qabstractslider.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qabstractspinbox.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qboxlayout.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qcombobox.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qdialog.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qfiledialog.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qframe.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qgridlayout.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qgroupbox.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qlabel.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qlayout.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qlayoutitem.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qmessagebox.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qpushbutton.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qrubberband.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qscrollarea.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qscrollbar.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qsizepolicy.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qslider.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qstackedwidget.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qstyle.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qstyleoption.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qtabbar.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qtabwidget.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qtwidgets-config.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qtwidgetsglobal.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qwidget.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o
 ../onnx/onnxruntime-linux-x64-1.15.1/include/onnxruntime_c_api.h
 ../onnx/onnxruntime-linux-x64-1.15.1/include/onnxruntime_cxx_api.h
 ../onnx/onnxruntime-linux-x64-1.15.1/include/onnxruntime_cxx_inline.h
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/DataStruct/FrameData.h
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/SuperEigen/include/ModelSession.h
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/SuperEigen/include/PrePostProcessor.h
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/SuperEigen/include/SuperResConfig.h
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/SuperEigen/include/SuperResEngine.h
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/UI/ImageProcessor.cpp
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/UI/ImageProcessor.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QAtomicInt
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QDebug
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QElapsedTimer
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QMutex
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QObject
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QQueue
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QRunnable
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QThread
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QThreadPool
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QWaitCondition
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qalgorithms.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qarraydata.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qatomic.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qatomic_bootstrap.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qatomic_cxx11.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qatomic_msvc.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qbasicatomic.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qbytearray.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qbytearraylist.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qchar.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qcompilerdetection.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qconfig-bootstrapped.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qconfig.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qcontainerfwd.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qcontiguouscache.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qcoreapplication.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qcoreevent.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qdatastream.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qdebug.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qelapsedtimer.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qeventloop.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qfile.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qfiledevice.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qflags.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qgenericatomic.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qglobal.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qglobalstatic.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qhash.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qhashfunctions.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qiodevice.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qiterator.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qline.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qlist.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qlocale.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qlogging.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qmap.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qmargins.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qmetatype.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qmutex.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qnamespace.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qnumeric.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qobject.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qobject_impl.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qobjectdefs.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qobjectdefs_impl.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qpair.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qpoint.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qprocessordetection.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qqueue.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qrect.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qrefcount.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qregexp.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qrunnable.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qscopedpointer.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qset.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qshareddata.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qsharedpointer.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qsharedpointer_impl.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qsize.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qstring.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qstringbuilder.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qstringlist.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qstringmatcher.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qsysinfo.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qsystemdetection.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qtcore-config.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qtextstream.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qthread.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qthreadpool.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qtypeinfo.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qurl.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qurlquery.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qvariant.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qvarlengtharray.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qvector.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qversiontagging.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qwaitcondition.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qbrush.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qcolor.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qcursor.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qevent.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qfont.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qfontinfo.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qfontmetrics.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qguiapplication.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qimage.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qinputmethod.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qkeysequence.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qmatrix.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qpaintdevice.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qpainterpath.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qpalette.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qpixelformat.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qpixmap.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qpolygon.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qregion.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qrgb.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qrgba64.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qtgui-config.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qtguiglobal.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qtouchdevice.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qtransform.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qvector2d.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qwindowdefs.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qwindowdefs_win.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QApplication
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qapplication.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qdesktopwidget.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qsizepolicy.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qtwidgets-config.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qtwidgetsglobal.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qwidget.h
 VideoSRLiteGUI_autogen/include/ImageProcessor.moc
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o
 ../onnx/onnxruntime-linux-x64-1.15.1/include/onnxruntime_c_api.h
 ../onnx/onnxruntime-linux-x64-1.15.1/include/onnxruntime_cxx_api.h
 ../onnx/onnxruntime-linux-x64-1.15.1/include/onnxruntime_cxx_inline.h
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/DataStruct/FrameData.h
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/SuperEigen/include/ModelSession.h
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/SuperEigen/include/PrePostProcessor.h
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/SuperEigen/include/SuperResConfig.h
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/SuperEigen/include/SuperResEngine.h
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/UI/FileListWidget.h
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/UI/ImagePreviewWidget.h
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/UI/ImageProcessor.h
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/UI/MainWindow.cpp
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/UI/MainWindow.h
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/UI/SettingsPanel.h
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/WorkerPool/WorkerPool.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QAtomicInt
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QDateTime
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QDebug
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QDir
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QElapsedTimer
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QFileInfo
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QMap
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QMimeData
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QMimeDatabase
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QMutex
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QObject
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QQueue
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QRunnable
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QSize
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QStandardPaths
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QStringList
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QThread
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QThreadPool
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QTimer
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QUrl
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QUuid
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QVariant
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QWaitCondition
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qabstractitemmodel.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qalgorithms.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qarraydata.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qatomic.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qatomic_bootstrap.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qatomic_cxx11.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qatomic_msvc.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qbasicatomic.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qbasictimer.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qbytearray.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qbytearraylist.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qchar.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qcompilerdetection.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qconfig-bootstrapped.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qconfig.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qcontainerfwd.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qcontiguouscache.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qcoreapplication.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qcoreevent.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qdatastream.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qdatetime.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qdebug.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qdir.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qelapsedtimer.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qeventloop.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qfile.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qfiledevice.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qfileinfo.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qflags.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qgenericatomic.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qglobal.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qglobalstatic.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qhash.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qhashfunctions.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qiodevice.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qitemselectionmodel.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qiterator.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qline.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qlist.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qlocale.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qlogging.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qmap.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qmargins.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qmetatype.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qmimedata.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qmimedatabase.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qmimetype.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qmutex.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qnamespace.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qnumeric.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qobject.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qobject_impl.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qobjectdefs.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qobjectdefs_impl.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qpair.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qpoint.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qprocessordetection.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qqueue.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qrect.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qrefcount.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qregexp.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qregularexpression.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qrunnable.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qscopedpointer.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qset.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qshareddata.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qsharedpointer.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qsharedpointer_impl.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qsize.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qstandardpaths.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qstring.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qstringbuilder.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qstringlist.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qstringmatcher.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qsysinfo.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qsystemdetection.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qtcore-config.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qtextstream.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qthread.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qthreadpool.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qtimer.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qtypeinfo.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qurl.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qurlquery.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/quuid.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qvariant.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qvarlengtharray.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qvector.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qversiontagging.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qwaitcondition.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/QCloseEvent
 /usr/include/x86_64-linux-gnu/qt5/QtGui/QDragEnterEvent
 /usr/include/x86_64-linux-gnu/qt5/QtGui/QDropEvent
 /usr/include/x86_64-linux-gnu/qt5/QtGui/QKeyEvent
 /usr/include/x86_64-linux-gnu/qt5/QtGui/QPainter
 /usr/include/x86_64-linux-gnu/qt5/QtGui/QPixmap
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qbrush.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qcolor.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qcursor.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qevent.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qfont.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qfontinfo.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qfontmetrics.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qguiapplication.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qicon.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qimage.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qinputmethod.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qkeysequence.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qmatrix.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qpaintdevice.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qpainter.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qpainterpath.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qpalette.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qpen.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qpixelformat.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qpixmap.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qpolygon.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qregion.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qrgb.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qrgba64.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qtextcursor.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qtextdocument.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qtextformat.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qtextoption.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qtgui-config.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qtguiglobal.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qtouchdevice.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qtransform.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qvalidator.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qvector2d.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qwindowdefs.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qwindowdefs_win.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QAction
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QApplication
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QButtonGroup
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QCheckBox
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QComboBox
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QDoubleSpinBox
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QFileDialog
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QGridLayout
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QGroupBox
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QHBoxLayout
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QHeaderView
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QLabel
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QLineEdit
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QListWidget
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QListWidgetItem
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QMainWindow
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QMenu
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QMenuBar
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QMessageBox
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QProgressBar
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QPushButton
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QRadioButton
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QScrollArea
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QScrollBar
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QSlider
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QSpacerItem
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QSpinBox
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QSplitter
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QStackedWidget
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QStatusBar
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QTextEdit
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QVBoxLayout
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QWidget
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qabstractbutton.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qabstractitemdelegate.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qabstractitemview.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qabstractscrollarea.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qabstractslider.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qabstractspinbox.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qaction.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qactiongroup.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qapplication.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qboxlayout.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qbuttongroup.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qcheckbox.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qcombobox.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qdesktopwidget.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qdialog.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qfiledialog.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qframe.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qgridlayout.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qgroupbox.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qheaderview.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qlabel.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qlayout.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qlayoutitem.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qlineedit.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qlistview.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qlistwidget.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qmainwindow.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qmenu.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qmenubar.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qmessagebox.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qprogressbar.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qpushbutton.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qradiobutton.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qrubberband.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qscrollarea.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qscrollbar.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qsizepolicy.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qslider.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qspinbox.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qsplitter.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qstackedwidget.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qstatusbar.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qstyle.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qstyleoption.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qtabbar.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qtabwidget.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qtextedit.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qtwidgets-config.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qtwidgetsglobal.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qwidget.h
 VideoSRLiteGUI_autogen/include/ui_MainWindow.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/UI/SettingsPanel.cpp
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/UI/SettingsPanel.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QDebug
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QDir
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QSettings
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QStandardPaths
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qabstractitemmodel.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qalgorithms.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qarraydata.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qatomic.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qatomic_bootstrap.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qatomic_cxx11.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qatomic_msvc.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qbasicatomic.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qbytearray.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qbytearraylist.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qchar.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qcompilerdetection.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qconfig-bootstrapped.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qconfig.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qcontainerfwd.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qcontiguouscache.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qcoreevent.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qdatastream.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qdebug.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qdir.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qfile.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qfiledevice.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qfileinfo.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qflags.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qgenericatomic.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qglobal.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qglobalstatic.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qhash.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qhashfunctions.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qiodevice.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qiterator.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qline.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qlist.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qlocale.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qlogging.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qmap.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qmargins.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qmetatype.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qmutex.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qnamespace.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qnumeric.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qobject.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qobject_impl.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qobjectdefs.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qobjectdefs_impl.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qpair.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qpoint.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qprocessordetection.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qrect.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qrefcount.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qregexp.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qregularexpression.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qscopedpointer.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qset.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qsettings.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qshareddata.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qsharedpointer.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qsharedpointer_impl.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qsize.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qstandardpaths.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qstring.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qstringbuilder.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qstringlist.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qstringmatcher.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qsysinfo.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qsystemdetection.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qtcore-config.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qtextstream.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qtypeinfo.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qurl.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qurlquery.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qvariant.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qvarlengtharray.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qvector.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qversiontagging.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qbrush.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qcolor.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qcursor.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qevent.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qfont.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qfontinfo.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qfontmetrics.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qicon.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qimage.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qkeysequence.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qmatrix.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qpaintdevice.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qpainterpath.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qpalette.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qpen.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qpixelformat.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qpixmap.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qpolygon.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qregion.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qrgb.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qrgba64.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qtextcursor.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qtextformat.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qtextoption.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qtgui-config.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qtguiglobal.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qtouchdevice.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qtransform.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qvalidator.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qvector2d.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qwindowdefs.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qwindowdefs_win.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QCheckBox
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QComboBox
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QDoubleSpinBox
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QFileDialog
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QGridLayout
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QGroupBox
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QHBoxLayout
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QInputDialog
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QLabel
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QLineEdit
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QMessageBox
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QPushButton
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QSlider
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QSpinBox
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QVBoxLayout
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QWidget
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qabstractbutton.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qabstractitemdelegate.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qabstractslider.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qabstractspinbox.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qboxlayout.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qcheckbox.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qcombobox.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qdialog.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qfiledialog.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qframe.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qgridlayout.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qgroupbox.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qinputdialog.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qlabel.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qlayout.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qlayoutitem.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qlineedit.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qmessagebox.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qpushbutton.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qrubberband.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qsizepolicy.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qslider.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qspinbox.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qstyle.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qstyleoption.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qtabbar.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qtabwidget.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qtwidgets-config.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qtwidgetsglobal.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qwidget.h
CMakeFiles/VideoSRLiteGUI.dir/src/Utils/FileUtils.cpp.o
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Utils/FileUtils.cpp
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Utils/FileUtils.h
CMakeFiles/VideoSRLiteGUI.dir/src/Utils/LogUtils.cpp.o
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Utils/LogUtils.cpp
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Utils/LogUtils.h
CMakeFiles/VideoSRLiteGUI.dir/src/Utils/Logger.cpp.o
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Utils/Logger.cpp
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Utils/Logger.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/WorkerPool/WorkerPool.cpp
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/WorkerPool/WorkerPool.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QDebug
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QMutex
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QObject
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QQueue
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QThread
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QWaitCondition
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qalgorithms.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qarraydata.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qatomic.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qatomic_bootstrap.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qatomic_cxx11.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qatomic_msvc.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qbasicatomic.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qbytearray.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qbytearraylist.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qchar.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qcompilerdetection.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qconfig-bootstrapped.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qconfig.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qcontainerfwd.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qcontiguouscache.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qcoreevent.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qdebug.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qflags.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qgenericatomic.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qglobal.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qglobalstatic.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qhash.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qhashfunctions.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qiodevice.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qiterator.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qlist.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qlocale.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qlogging.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qmap.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qmetatype.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qmutex.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qnamespace.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qnumeric.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qobject.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qobject_impl.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qobjectdefs.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qobjectdefs_impl.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qpair.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qpoint.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qprocessordetection.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qqueue.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qrefcount.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qregexp.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qscopedpointer.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qset.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qshareddata.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qsharedpointer.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qsharedpointer_impl.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qstring.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qstringbuilder.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qstringlist.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qstringmatcher.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qsysinfo.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qsystemdetection.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qtcore-config.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qtextstream.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qthread.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qtypeinfo.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qvariant.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qvarlengtharray.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qvector.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qversiontagging.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qwaitcondition.h
 VideoSRLiteGUI_autogen/include/WorkerPool.moc
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o
 ../onnx/onnxruntime-linux-x64-1.15.1/include/onnxruntime_c_api.h
 ../onnx/onnxruntime-linux-x64-1.15.1/include/onnxruntime_cxx_api.h
 ../onnx/onnxruntime-linux-x64-1.15.1/include/onnxruntime_cxx_inline.h
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/DataStruct/FrameData.h
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/SuperEigen/include/ModelSession.h
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/SuperEigen/include/PrePostProcessor.h
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/SuperEigen/include/SuperResConfig.h
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/SuperEigen/include/SuperResEngine.h
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/UI/FileListWidget.h
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/UI/ImagePreviewWidget.h
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/UI/ImageProcessor.h
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/UI/MainWindow.h
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/UI/SettingsPanel.h
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/WorkerPool/WorkerPool.h
 /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/main.cpp
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QAtomicInt
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QDateTime
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QElapsedTimer
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QFileInfo
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QMap
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QMetaType
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QMimeData
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QMutex
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QObject
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QQueue
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QRunnable
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QSize
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QStandardPaths
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QStringList
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QThread
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QThreadPool
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QTimer
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QUrl
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QWaitCondition
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qabstractitemmodel.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qalgorithms.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qarraydata.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qatomic.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qatomic_bootstrap.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qatomic_cxx11.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qatomic_msvc.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qbasicatomic.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qbasictimer.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qbytearray.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qbytearraylist.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qchar.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qcompilerdetection.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qconfig-bootstrapped.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qconfig.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qcontainerfwd.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qcontiguouscache.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qcoreapplication.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qcoreevent.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qdatastream.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qdatetime.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qdebug.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qdir.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qelapsedtimer.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qeventloop.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qfile.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qfiledevice.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qfileinfo.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qflags.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qgenericatomic.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qglobal.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qglobalstatic.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qhash.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qhashfunctions.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qiodevice.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qitemselectionmodel.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qiterator.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qline.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qlist.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qlocale.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qlogging.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qmap.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qmargins.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qmetatype.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qmimedata.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qmutex.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qnamespace.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qnumeric.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qobject.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qobject_impl.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qobjectdefs.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qobjectdefs_impl.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qpair.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qpoint.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qprocessordetection.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qqueue.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qrect.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qrefcount.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qregexp.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qregularexpression.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qrunnable.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qscopedpointer.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qset.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qshareddata.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qsharedpointer.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qsharedpointer_impl.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qsize.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qstandardpaths.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qstring.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qstringbuilder.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qstringlist.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qstringmatcher.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qsysinfo.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qsystemdetection.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qtcore-config.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qtextstream.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qthread.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qthreadpool.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qtimer.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qtypeinfo.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qurl.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qurlquery.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qvariant.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qvarlengtharray.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qvector.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qversiontagging.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qwaitcondition.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/QCloseEvent
 /usr/include/x86_64-linux-gnu/qt5/QtGui/QDragEnterEvent
 /usr/include/x86_64-linux-gnu/qt5/QtGui/QDropEvent
 /usr/include/x86_64-linux-gnu/qt5/QtGui/QPixmap
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qbrush.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qcolor.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qcursor.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qevent.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qfont.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qfontinfo.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qfontmetrics.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qguiapplication.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qicon.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qimage.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qinputmethod.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qkeysequence.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qmatrix.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qpaintdevice.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qpainterpath.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qpalette.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qpen.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qpixelformat.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qpixmap.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qpolygon.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qregion.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qrgb.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qrgba64.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qtextcursor.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qtextdocument.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qtextformat.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qtextoption.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qtgui-config.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qtguiglobal.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qtouchdevice.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qtransform.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qvalidator.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qvector2d.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qwindowdefs.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qwindowdefs_win.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QApplication
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QCheckBox
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QComboBox
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QDoubleSpinBox
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QFileDialog
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QGridLayout
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QGroupBox
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QHBoxLayout
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QLabel
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QLineEdit
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QListWidget
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QListWidgetItem
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QMainWindow
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QMessageBox
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QProgressBar
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QPushButton
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QScrollArea
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QScrollBar
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QSlider
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QSpinBox
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QSplitter
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QStackedWidget
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QTextEdit
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QVBoxLayout
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QWidget
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qabstractbutton.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qabstractitemdelegate.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qabstractitemview.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qabstractscrollarea.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qabstractslider.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qabstractspinbox.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qapplication.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qboxlayout.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qcheckbox.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qcombobox.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qdesktopwidget.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qdialog.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qfiledialog.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qframe.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qgridlayout.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qgroupbox.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qlabel.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qlayout.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qlayoutitem.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qlineedit.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qlistview.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qlistwidget.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qmainwindow.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qmessagebox.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qprogressbar.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qpushbutton.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qrubberband.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qscrollarea.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qscrollbar.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qsizepolicy.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qslider.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qspinbox.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qsplitter.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qstackedwidget.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qstyle.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qstyleoption.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qtabbar.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qtabwidget.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qtextedit.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qtwidgets-config.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qtwidgetsglobal.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qwidget.h
