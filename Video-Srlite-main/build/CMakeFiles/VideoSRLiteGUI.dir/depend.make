# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.10

CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: ../onnx/onnxruntime-linux-x64-1.15.1/include/onnxruntime_c_api.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: ../onnx/onnxruntime-linux-x64-1.15.1/include/onnxruntime_cxx_api.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: ../onnx/onnxruntime-linux-x64-1.15.1/include/onnxruntime_cxx_inline.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: VideoSRLiteGUI_autogen/HC4UDM2PON/moc_WorkerPool.cpp
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: VideoSRLiteGUI_autogen/WAYUIA5GRM/moc_FileListWidget.cpp
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: VideoSRLiteGUI_autogen/WAYUIA5GRM/moc_ImagePreviewWidget.cpp
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: VideoSRLiteGUI_autogen/WAYUIA5GRM/moc_ImageProcessor.cpp
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: VideoSRLiteGUI_autogen/WAYUIA5GRM/moc_MainWindow.cpp
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: VideoSRLiteGUI_autogen/WAYUIA5GRM/moc_SettingsPanel.cpp
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: VideoSRLiteGUI_autogen/mocs_compilation.cpp
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: ../src/DataStruct/FrameData.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: ../src/SuperEigen/include/ModelSession.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: ../src/SuperEigen/include/PrePostProcessor.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: ../src/SuperEigen/include/SuperResConfig.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: ../src/SuperEigen/include/SuperResEngine.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: ../src/UI/FileListWidget.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: ../src/UI/ImagePreviewWidget.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: ../src/UI/ImageProcessor.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: ../src/UI/MainWindow.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: ../src/UI/SettingsPanel.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: ../src/WorkerPool/WorkerPool.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/QAtomicInt
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/QDateTime
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/QDeadlineTimer
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/QElapsedTimer
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/QFileInfo
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/QMap
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/QMimeData
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/QMutex
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/QObject
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/QQueue
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/QRunnable
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/QSize
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/QStandardPaths
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/QStringList
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/QThread
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/QThreadPool
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/QTimer
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/QUrl
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/QWaitCondition
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/qabstractitemmodel.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/qalgorithms.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/qarraydata.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/qatomic.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/qatomic_bootstrap.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/qatomic_cxx11.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/qatomic_msvc.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/qbasicatomic.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/qbasictimer.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/qbytearray.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/qbytearraylist.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/qchar.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/qcompilerdetection.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/qconfig-bootstrapped.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/qconfig.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/qcontainerfwd.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/qcontainertools_impl.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/qcontiguouscache.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/qcoreapplication.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/qcoreevent.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/qdatastream.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/qdatetime.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/qdeadlinetimer.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/qdebug.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/qdir.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/qelapsedtimer.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/qeventloop.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/qfile.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/qfiledevice.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/qfileinfo.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/qflags.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/qgenericatomic.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/qglobal.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/qglobalstatic.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/qhash.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/qhashfunctions.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/qiodevice.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/qitemselectionmodel.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/qiterator.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/qline.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/qlist.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/qlocale.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/qlogging.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/qmap.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/qmargins.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/qmetatype.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/qmimedata.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/qmutex.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/qnamespace.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/qnumeric.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/qobject.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/qobject_impl.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/qobjectdefs.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/qobjectdefs_impl.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/qpair.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/qpoint.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/qprocessordetection.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/qqueue.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/qrect.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/qrefcount.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/qregexp.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/qregularexpression.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/qrunnable.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/qscopedpointer.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/qset.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/qshareddata.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/qsharedpointer.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/qsharedpointer_impl.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/qsize.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/qstandardpaths.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/qstring.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/qstringalgorithms.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/qstringbuilder.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/qstringlist.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/qstringliteral.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/qstringmatcher.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/qstringview.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/qsysinfo.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/qsystemdetection.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/qtcore-config.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/qtextstream.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/qthread.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/qthreadpool.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/qtimer.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/qtypeinfo.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/qurl.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/qurlquery.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/qvariant.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/qvarlengtharray.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/qvector.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/qversiontagging.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtCore/qwaitcondition.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtGui/QCloseEvent
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtGui/QDragEnterEvent
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtGui/QDropEvent
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtGui/QPixmap
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtGui/qbrush.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtGui/qcolor.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtGui/qcursor.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtGui/qevent.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtGui/qfont.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtGui/qfontinfo.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtGui/qfontmetrics.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtGui/qguiapplication.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtGui/qicon.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtGui/qimage.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtGui/qinputmethod.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtGui/qkeysequence.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtGui/qmatrix.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtGui/qpaintdevice.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtGui/qpalette.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtGui/qpen.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtGui/qpixelformat.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtGui/qpixmap.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtGui/qpolygon.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtGui/qregion.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtGui/qrgb.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtGui/qrgba64.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtGui/qtextcursor.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtGui/qtextdocument.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtGui/qtextformat.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtGui/qtextoption.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtGui/qtgui-config.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtGui/qtguiglobal.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtGui/qtouchdevice.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtGui/qtransform.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtGui/qvalidator.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtGui/qvector2d.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtGui/qwindowdefs.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtGui/qwindowdefs_win.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtWidgets/QApplication
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtWidgets/QCheckBox
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtWidgets/QComboBox
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtWidgets/QDoubleSpinBox
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtWidgets/QFileDialog
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtWidgets/QGridLayout
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtWidgets/QGroupBox
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtWidgets/QHBoxLayout
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtWidgets/QLabel
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtWidgets/QLineEdit
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtWidgets/QListWidget
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtWidgets/QListWidgetItem
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtWidgets/QMainWindow
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtWidgets/QMessageBox
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtWidgets/QProgressBar
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtWidgets/QPushButton
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtWidgets/QScrollArea
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtWidgets/QScrollBar
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtWidgets/QSlider
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtWidgets/QSpinBox
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtWidgets/QSplitter
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtWidgets/QStackedWidget
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtWidgets/QTextEdit
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtWidgets/QVBoxLayout
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtWidgets/QWidget
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtWidgets/qabstractbutton.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtWidgets/qabstractitemdelegate.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtWidgets/qabstractitemview.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtWidgets/qabstractscrollarea.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtWidgets/qabstractslider.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtWidgets/qabstractspinbox.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtWidgets/qapplication.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtWidgets/qboxlayout.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtWidgets/qcheckbox.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtWidgets/qcombobox.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtWidgets/qdesktopwidget.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtWidgets/qdialog.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtWidgets/qfiledialog.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtWidgets/qframe.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtWidgets/qgridlayout.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtWidgets/qgroupbox.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtWidgets/qlabel.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtWidgets/qlayout.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtWidgets/qlayoutitem.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtWidgets/qlineedit.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtWidgets/qlistview.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtWidgets/qlistwidget.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtWidgets/qmainwindow.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtWidgets/qmessagebox.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtWidgets/qprogressbar.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtWidgets/qpushbutton.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtWidgets/qrubberband.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtWidgets/qscrollarea.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtWidgets/qscrollbar.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtWidgets/qsizepolicy.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtWidgets/qslider.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtWidgets/qspinbox.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtWidgets/qsplitter.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtWidgets/qstackedwidget.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtWidgets/qstyle.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtWidgets/qstyleoption.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtWidgets/qtabbar.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtWidgets/qtabwidget.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtWidgets/qtextedit.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtWidgets/qtwidgets-config.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtWidgets/qtwidgetsglobal.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /opt/qt515/include/QtWidgets/qwidget.h

CMakeFiles/VideoSRLiteGUI.dir/src/AppController/AppController.cpp.o: ../src/AppController/AppController.cpp

CMakeFiles/VideoSRLiteGUI.dir/src/AppController/WorkerPool.cpp.o: ../src/AppController/WorkerPool.cpp

CMakeFiles/VideoSRLiteGUI.dir/src/AudioProc/AudioProc.cpp.o: ../src/AudioProc/AudioProc.cpp

CMakeFiles/VideoSRLiteGUI.dir/src/AudioProcessor/AudioProcessor.cpp.o: ../src/AudioProcessor/AudioProcessor.cpp

CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/AudioDecoder.cpp.o: ../src/DataStruct/AudioFrameData.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/AudioDecoder.cpp.o: ../src/Decoder/include/AudioDecoder.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/AudioDecoder.cpp.o: ../src/Decoder/src/AudioDecoder.cpp
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/AudioDecoder.cpp.o: ../src/Utils/Logger.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/AudioDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/avcodec.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/AudioDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/bsf.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/AudioDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/codec.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/AudioDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/codec_desc.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/AudioDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/codec_id.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/AudioDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/codec_par.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/AudioDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/packet.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/AudioDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/version.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/AudioDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavformat/avformat.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/AudioDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavformat/avio.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/AudioDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavformat/version.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/AudioDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/attributes.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/AudioDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/avconfig.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/AudioDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/avutil.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/AudioDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/buffer.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/AudioDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/channel_layout.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/AudioDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/common.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/AudioDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/cpu.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/AudioDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/dict.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/AudioDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/error.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/AudioDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/frame.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/AudioDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/hwcontext.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/AudioDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/intfloat.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/AudioDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/log.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/AudioDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/macros.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/AudioDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/mathematics.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/AudioDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/mem.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/AudioDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/opt.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/AudioDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/pixfmt.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/AudioDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/rational.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/AudioDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/samplefmt.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/AudioDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/version.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/AudioDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libswresample/swresample.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/AudioDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libswresample/version.h

CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/Decoder.cpp.o: ../src/DataStruct/AudioFrameData.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/Decoder.cpp.o: ../src/DataStruct/FrameData.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/Decoder.cpp.o: ../src/Decoder/include/AudioDecoder.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/Decoder.cpp.o: ../src/Decoder/include/Decoder.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/Decoder.cpp.o: ../src/Decoder/include/VideoDecoder.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/Decoder.cpp.o: ../src/Decoder/src/Decoder.cpp
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/Decoder.cpp.o: ../src/Utils/Logger.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/Decoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/avcodec.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/Decoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/bsf.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/Decoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/codec.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/Decoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/codec_desc.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/Decoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/codec_id.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/Decoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/codec_par.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/Decoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/packet.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/Decoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/version.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/Decoder.cpp.o: /usr/include/x86_64-linux-gnu/libavformat/avformat.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/Decoder.cpp.o: /usr/include/x86_64-linux-gnu/libavformat/avio.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/Decoder.cpp.o: /usr/include/x86_64-linux-gnu/libavformat/version.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/Decoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/attributes.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/Decoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/avconfig.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/Decoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/avutil.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/Decoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/buffer.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/Decoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/channel_layout.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/Decoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/common.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/Decoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/cpu.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/Decoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/dict.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/Decoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/error.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/Decoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/frame.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/Decoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/hwcontext.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/Decoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/intfloat.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/Decoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/log.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/Decoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/macros.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/Decoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/mathematics.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/Decoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/mem.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/Decoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/pixdesc.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/Decoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/pixfmt.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/Decoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/rational.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/Decoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/samplefmt.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/Decoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/version.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/Decoder.cpp.o: /usr/include/x86_64-linux-gnu/libswresample/swresample.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/Decoder.cpp.o: /usr/include/x86_64-linux-gnu/libswresample/version.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/Decoder.cpp.o: /usr/include/x86_64-linux-gnu/libswscale/swscale.h

CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/VideoDecoder.cpp.o: ../src/DataStruct/FrameData.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/VideoDecoder.cpp.o: ../src/Decoder/include/VideoDecoder.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/VideoDecoder.cpp.o: ../src/Decoder/src/VideoDecoder.cpp
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/VideoDecoder.cpp.o: ../src/Utils/Logger.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/VideoDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/avcodec.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/VideoDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/bsf.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/VideoDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/codec.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/VideoDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/codec_desc.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/VideoDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/codec_id.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/VideoDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/codec_par.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/VideoDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/packet.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/VideoDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/version.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/VideoDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavformat/avformat.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/VideoDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavformat/avio.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/VideoDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavformat/version.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/VideoDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/attributes.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/VideoDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/avconfig.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/VideoDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/avutil.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/VideoDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/buffer.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/VideoDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/channel_layout.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/VideoDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/common.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/VideoDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/cpu.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/VideoDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/dict.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/VideoDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/error.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/VideoDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/frame.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/VideoDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/hwcontext.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/VideoDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/intfloat.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/VideoDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/log.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/VideoDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/macros.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/VideoDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/mathematics.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/VideoDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/mem.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/VideoDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/pixdesc.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/VideoDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/pixfmt.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/VideoDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/rational.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/VideoDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/samplefmt.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/VideoDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/version.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/VideoDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libswscale/swscale.h

CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/AudioEncoder.cpp.o: ../src/DataStruct/AudioFrameData.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/AudioEncoder.cpp.o: ../src/Encoder/AudioEncoder.cpp
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/AudioEncoder.cpp.o: ../src/Encoder/AudioEncoder.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/AudioEncoder.cpp.o: ../src/Utils/Logger.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/AudioEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/avcodec.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/AudioEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/bsf.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/AudioEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/codec.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/AudioEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/codec_desc.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/AudioEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/codec_id.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/AudioEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/codec_par.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/AudioEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/packet.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/AudioEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/version.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/AudioEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavformat/avformat.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/AudioEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavformat/avio.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/AudioEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavformat/version.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/AudioEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/attributes.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/AudioEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/avconfig.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/AudioEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/avutil.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/AudioEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/buffer.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/AudioEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/channel_layout.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/AudioEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/common.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/AudioEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/cpu.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/AudioEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/dict.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/AudioEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/error.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/AudioEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/frame.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/AudioEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/hwcontext.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/AudioEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/intfloat.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/AudioEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/log.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/AudioEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/macros.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/AudioEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/mathematics.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/AudioEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/mem.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/AudioEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/opt.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/AudioEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/pixfmt.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/AudioEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/rational.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/AudioEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/samplefmt.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/AudioEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/version.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/AudioEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libswresample/swresample.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/AudioEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libswresample/version.h

CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o: ../src/DataStruct/AudioFrameData.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o: ../src/DataStruct/FrameData.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o: ../src/Encoder/AudioEncoder.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o: ../src/Encoder/Encoder.cpp
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o: ../src/Encoder/Encoder.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o: ../src/Encoder/Muxer.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o: ../src/Encoder/VideoEncoder.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o: ../src/Utils/Logger.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/avcodec.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/bsf.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/codec.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/codec_desc.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/codec_id.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/codec_par.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/packet.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/version.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o: /usr/include/x86_64-linux-gnu/libavformat/avformat.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o: /usr/include/x86_64-linux-gnu/libavformat/avio.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o: /usr/include/x86_64-linux-gnu/libavformat/version.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/attributes.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/avconfig.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/avutil.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/buffer.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/channel_layout.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/common.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/cpu.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/dict.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/error.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/frame.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/hwcontext.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/imgutils.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/intfloat.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/log.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/macros.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/mathematics.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/mem.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/opt.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/pixdesc.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/pixfmt.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/rational.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/samplefmt.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/version.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o: /usr/include/x86_64-linux-gnu/libswresample/swresample.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o: /usr/include/x86_64-linux-gnu/libswresample/version.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o: /usr/include/x86_64-linux-gnu/libswscale/swscale.h

CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Muxer.cpp.o: ../src/Encoder/Muxer.cpp
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Muxer.cpp.o: ../src/Encoder/Muxer.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Muxer.cpp.o: ../src/Utils/Logger.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Muxer.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/avcodec.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Muxer.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/bsf.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Muxer.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/codec.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Muxer.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/codec_desc.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Muxer.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/codec_id.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Muxer.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/codec_par.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Muxer.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/packet.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Muxer.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/version.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Muxer.cpp.o: /usr/include/x86_64-linux-gnu/libavformat/avformat.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Muxer.cpp.o: /usr/include/x86_64-linux-gnu/libavformat/avio.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Muxer.cpp.o: /usr/include/x86_64-linux-gnu/libavformat/version.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Muxer.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/attributes.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Muxer.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/avconfig.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Muxer.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/avutil.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Muxer.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/buffer.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Muxer.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/channel_layout.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Muxer.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/common.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Muxer.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/cpu.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Muxer.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/dict.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Muxer.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/error.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Muxer.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/frame.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Muxer.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/hwcontext.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Muxer.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/intfloat.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Muxer.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/log.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Muxer.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/macros.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Muxer.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/mathematics.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Muxer.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/mem.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Muxer.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/pixfmt.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Muxer.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/rational.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Muxer.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/samplefmt.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Muxer.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/version.h

CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/VideoEncoder.cpp.o: ../src/DataStruct/FrameData.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/VideoEncoder.cpp.o: ../src/Encoder/VideoEncoder.cpp
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/VideoEncoder.cpp.o: ../src/Encoder/VideoEncoder.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/VideoEncoder.cpp.o: ../src/Utils/Logger.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/VideoEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/avcodec.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/VideoEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/bsf.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/VideoEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/codec.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/VideoEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/codec_desc.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/VideoEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/codec_id.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/VideoEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/codec_par.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/VideoEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/packet.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/VideoEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/version.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/VideoEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavformat/avformat.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/VideoEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavformat/avio.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/VideoEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavformat/version.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/VideoEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/attributes.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/VideoEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/avconfig.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/VideoEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/avutil.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/VideoEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/buffer.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/VideoEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/channel_layout.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/VideoEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/common.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/VideoEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/cpu.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/VideoEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/dict.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/VideoEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/error.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/VideoEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/frame.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/VideoEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/hwcontext.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/VideoEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/imgutils.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/VideoEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/intfloat.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/VideoEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/log.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/VideoEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/macros.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/VideoEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/mathematics.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/VideoEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/mem.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/VideoEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/opt.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/VideoEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/pixdesc.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/VideoEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/pixfmt.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/VideoEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/rational.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/VideoEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/samplefmt.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/VideoEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/version.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/VideoEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libswscale/swscale.h

CMakeFiles/VideoSRLiteGUI.dir/src/PostFilter/PostFilter.cpp.o: ../src/PostFilter/PostFilter.cpp

CMakeFiles/VideoSRLiteGUI.dir/src/PostFilter/PostFilterProcessor.cpp.o: ../src/PostFilter/PostFilterProcessor.cpp

CMakeFiles/VideoSRLiteGUI.dir/src/Processing/AudioDenoiser.cpp.o: ../src/Processing/AudioDenoiser.cpp
CMakeFiles/VideoSRLiteGUI.dir/src/Processing/AudioDenoiser.cpp.o: ../src/Processing/AudioDenoiser.h

CMakeFiles/VideoSRLiteGUI.dir/src/Processing/PostProcessor.cpp.o: ../src/Processing/PostProcessor.cpp
CMakeFiles/VideoSRLiteGUI.dir/src/Processing/PostProcessor.cpp.o: ../src/Processing/PostProcessor.h

CMakeFiles/VideoSRLiteGUI.dir/src/Processing/SuperResolution.cpp.o: ../onnx/onnxruntime-linux-x64-1.15.1/include/onnxruntime_c_api.h
CMakeFiles/VideoSRLiteGUI.dir/src/Processing/SuperResolution.cpp.o: ../onnx/onnxruntime-linux-x64-1.15.1/include/onnxruntime_cxx_api.h
CMakeFiles/VideoSRLiteGUI.dir/src/Processing/SuperResolution.cpp.o: ../onnx/onnxruntime-linux-x64-1.15.1/include/onnxruntime_cxx_inline.h
CMakeFiles/VideoSRLiteGUI.dir/src/Processing/SuperResolution.cpp.o: ../src/DataStruct/FrameData.h
CMakeFiles/VideoSRLiteGUI.dir/src/Processing/SuperResolution.cpp.o: ../src/Processing/SuperResolution.cpp
CMakeFiles/VideoSRLiteGUI.dir/src/Processing/SuperResolution.cpp.o: ../src/Processing/SuperResolution.h
CMakeFiles/VideoSRLiteGUI.dir/src/Processing/SuperResolution.cpp.o: ../src/SuperEigen/include/ModelSession.h
CMakeFiles/VideoSRLiteGUI.dir/src/Processing/SuperResolution.cpp.o: ../src/SuperEigen/include/PrePostProcessor.h
CMakeFiles/VideoSRLiteGUI.dir/src/Processing/SuperResolution.cpp.o: ../src/SuperEigen/include/SuperResConfig.h
CMakeFiles/VideoSRLiteGUI.dir/src/Processing/SuperResolution.cpp.o: ../src/SuperEigen/include/SuperResEngine.h
CMakeFiles/VideoSRLiteGUI.dir/src/Processing/SuperResolution.cpp.o: ../src/Utils/Logger.h

CMakeFiles/VideoSRLiteGUI.dir/src/SuperEigen/src/ModelSession.cpp.o: ../onnx/onnxruntime-linux-x64-1.15.1/include/onnxruntime_c_api.h
CMakeFiles/VideoSRLiteGUI.dir/src/SuperEigen/src/ModelSession.cpp.o: ../onnx/onnxruntime-linux-x64-1.15.1/include/onnxruntime_cxx_api.h
CMakeFiles/VideoSRLiteGUI.dir/src/SuperEigen/src/ModelSession.cpp.o: ../onnx/onnxruntime-linux-x64-1.15.1/include/onnxruntime_cxx_inline.h
CMakeFiles/VideoSRLiteGUI.dir/src/SuperEigen/src/ModelSession.cpp.o: ../src/SuperEigen/include/ModelSession.h
CMakeFiles/VideoSRLiteGUI.dir/src/SuperEigen/src/ModelSession.cpp.o: ../src/SuperEigen/include/SuperResConfig.h
CMakeFiles/VideoSRLiteGUI.dir/src/SuperEigen/src/ModelSession.cpp.o: ../src/SuperEigen/src/ModelSession.cpp
CMakeFiles/VideoSRLiteGUI.dir/src/SuperEigen/src/ModelSession.cpp.o: ../src/Utils/Logger.h

CMakeFiles/VideoSRLiteGUI.dir/src/SuperEigen/src/PrePostProcessor.cpp.o: ../onnx/onnxruntime-linux-x64-1.15.1/include/onnxruntime_c_api.h
CMakeFiles/VideoSRLiteGUI.dir/src/SuperEigen/src/PrePostProcessor.cpp.o: ../onnx/onnxruntime-linux-x64-1.15.1/include/onnxruntime_cxx_api.h
CMakeFiles/VideoSRLiteGUI.dir/src/SuperEigen/src/PrePostProcessor.cpp.o: ../onnx/onnxruntime-linux-x64-1.15.1/include/onnxruntime_cxx_inline.h
CMakeFiles/VideoSRLiteGUI.dir/src/SuperEigen/src/PrePostProcessor.cpp.o: ../src/SuperEigen/include/PrePostProcessor.h
CMakeFiles/VideoSRLiteGUI.dir/src/SuperEigen/src/PrePostProcessor.cpp.o: ../src/SuperEigen/include/SuperResConfig.h
CMakeFiles/VideoSRLiteGUI.dir/src/SuperEigen/src/PrePostProcessor.cpp.o: ../src/SuperEigen/src/PrePostProcessor.cpp

CMakeFiles/VideoSRLiteGUI.dir/src/SuperEigen/src/SuperResConfig.cpp.o: ../onnx/onnxruntime-linux-x64-1.15.1/include/onnxruntime_c_api.h
CMakeFiles/VideoSRLiteGUI.dir/src/SuperEigen/src/SuperResConfig.cpp.o: ../onnx/onnxruntime-linux-x64-1.15.1/include/onnxruntime_cxx_api.h
CMakeFiles/VideoSRLiteGUI.dir/src/SuperEigen/src/SuperResConfig.cpp.o: ../onnx/onnxruntime-linux-x64-1.15.1/include/onnxruntime_cxx_inline.h
CMakeFiles/VideoSRLiteGUI.dir/src/SuperEigen/src/SuperResConfig.cpp.o: ../src/SuperEigen/include/SuperResConfig.h
CMakeFiles/VideoSRLiteGUI.dir/src/SuperEigen/src/SuperResConfig.cpp.o: ../src/SuperEigen/src/SuperResConfig.cpp

CMakeFiles/VideoSRLiteGUI.dir/src/SuperEigen/src/SuperResEngine.cpp.o: ../onnx/onnxruntime-linux-x64-1.15.1/include/onnxruntime_c_api.h
CMakeFiles/VideoSRLiteGUI.dir/src/SuperEigen/src/SuperResEngine.cpp.o: ../onnx/onnxruntime-linux-x64-1.15.1/include/onnxruntime_cxx_api.h
CMakeFiles/VideoSRLiteGUI.dir/src/SuperEigen/src/SuperResEngine.cpp.o: ../onnx/onnxruntime-linux-x64-1.15.1/include/onnxruntime_cxx_inline.h
CMakeFiles/VideoSRLiteGUI.dir/src/SuperEigen/src/SuperResEngine.cpp.o: ../src/DataStruct/FrameData.h
CMakeFiles/VideoSRLiteGUI.dir/src/SuperEigen/src/SuperResEngine.cpp.o: ../src/SuperEigen/include/ModelSession.h
CMakeFiles/VideoSRLiteGUI.dir/src/SuperEigen/src/SuperResEngine.cpp.o: ../src/SuperEigen/include/PrePostProcessor.h
CMakeFiles/VideoSRLiteGUI.dir/src/SuperEigen/src/SuperResEngine.cpp.o: ../src/SuperEigen/include/SuperResConfig.h
CMakeFiles/VideoSRLiteGUI.dir/src/SuperEigen/src/SuperResEngine.cpp.o: ../src/SuperEigen/include/SuperResEngine.h
CMakeFiles/VideoSRLiteGUI.dir/src/SuperEigen/src/SuperResEngine.cpp.o: ../src/SuperEigen/src/SuperResEngine.cpp
CMakeFiles/VideoSRLiteGUI.dir/src/SuperEigen/src/SuperResEngine.cpp.o: ../src/Utils/Logger.h

CMakeFiles/VideoSRLiteGUI.dir/src/SyncVA/AVSyncManager.cpp.o: ../src/DataStruct/AudioFrameData.h
CMakeFiles/VideoSRLiteGUI.dir/src/SyncVA/AVSyncManager.cpp.o: ../src/DataStruct/FrameData.h
CMakeFiles/VideoSRLiteGUI.dir/src/SyncVA/AVSyncManager.cpp.o: ../src/SyncVA/AVSyncManager.cpp
CMakeFiles/VideoSRLiteGUI.dir/src/SyncVA/AVSyncManager.cpp.o: ../src/SyncVA/AVSyncManager.h
CMakeFiles/VideoSRLiteGUI.dir/src/SyncVA/AVSyncManager.cpp.o: ../src/Utils/Logger.h

CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: ../src/UI/FileListWidget.cpp
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: ../src/UI/FileListWidget.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtCore/QDateTime
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtCore/QDebug
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtCore/QFileInfo
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtCore/QMimeData
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtCore/QMimeDatabase
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtCore/QStandardPaths
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtCore/QUrl
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtCore/qabstractitemmodel.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtCore/qalgorithms.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtCore/qarraydata.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtCore/qatomic.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtCore/qatomic_bootstrap.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtCore/qatomic_cxx11.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtCore/qatomic_msvc.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtCore/qbasicatomic.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtCore/qbytearray.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtCore/qbytearraylist.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtCore/qchar.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtCore/qcompilerdetection.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtCore/qconfig-bootstrapped.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtCore/qconfig.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtCore/qcontainerfwd.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtCore/qcontainertools_impl.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtCore/qcontiguouscache.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtCore/qcoreapplication.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtCore/qcoreevent.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtCore/qdatastream.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtCore/qdatetime.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtCore/qdebug.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtCore/qdir.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtCore/qeventloop.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtCore/qfile.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtCore/qfiledevice.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtCore/qfileinfo.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtCore/qflags.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtCore/qgenericatomic.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtCore/qglobal.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtCore/qglobalstatic.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtCore/qhash.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtCore/qhashfunctions.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtCore/qiodevice.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtCore/qitemselectionmodel.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtCore/qiterator.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtCore/qline.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtCore/qlist.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtCore/qlocale.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtCore/qlogging.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtCore/qmap.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtCore/qmargins.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtCore/qmetatype.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtCore/qmimedata.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtCore/qmimedatabase.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtCore/qmimetype.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtCore/qmutex.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtCore/qnamespace.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtCore/qnumeric.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtCore/qobject.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtCore/qobject_impl.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtCore/qobjectdefs.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtCore/qobjectdefs_impl.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtCore/qpair.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtCore/qpoint.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtCore/qprocessordetection.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtCore/qrect.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtCore/qrefcount.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtCore/qregexp.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtCore/qregularexpression.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtCore/qscopedpointer.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtCore/qset.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtCore/qshareddata.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtCore/qsharedpointer.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtCore/qsharedpointer_impl.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtCore/qsize.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtCore/qstandardpaths.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtCore/qstring.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtCore/qstringalgorithms.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtCore/qstringbuilder.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtCore/qstringlist.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtCore/qstringliteral.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtCore/qstringmatcher.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtCore/qstringview.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtCore/qsysinfo.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtCore/qsystemdetection.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtCore/qtcore-config.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtCore/qtextstream.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtCore/qtypeinfo.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtCore/qurl.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtCore/qurlquery.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtCore/qvariant.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtCore/qvarlengtharray.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtCore/qvector.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtCore/qversiontagging.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtGui/QDragEnterEvent
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtGui/QDropEvent
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtGui/qbrush.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtGui/qcolor.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtGui/qcursor.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtGui/qevent.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtGui/qfont.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtGui/qfontinfo.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtGui/qfontmetrics.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtGui/qguiapplication.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtGui/qicon.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtGui/qimage.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtGui/qinputmethod.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtGui/qkeysequence.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtGui/qmatrix.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtGui/qpaintdevice.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtGui/qpalette.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtGui/qpixelformat.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtGui/qpixmap.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtGui/qpolygon.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtGui/qregion.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtGui/qrgb.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtGui/qrgba64.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtGui/qtgui-config.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtGui/qtguiglobal.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtGui/qtouchdevice.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtGui/qtransform.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtGui/qvalidator.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtGui/qvector2d.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtGui/qwindowdefs.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtGui/qwindowdefs_win.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtWidgets/QApplication
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtWidgets/QFileDialog
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtWidgets/QGroupBox
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtWidgets/QHBoxLayout
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtWidgets/QLabel
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtWidgets/QListWidget
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtWidgets/QListWidgetItem
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtWidgets/QMessageBox
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtWidgets/QProgressBar
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtWidgets/QPushButton
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtWidgets/QVBoxLayout
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtWidgets/QWidget
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtWidgets/qabstractbutton.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtWidgets/qabstractitemdelegate.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtWidgets/qabstractitemview.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtWidgets/qabstractscrollarea.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtWidgets/qabstractslider.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtWidgets/qabstractspinbox.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtWidgets/qapplication.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtWidgets/qboxlayout.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtWidgets/qdesktopwidget.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtWidgets/qdialog.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtWidgets/qfiledialog.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtWidgets/qframe.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtWidgets/qgridlayout.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtWidgets/qgroupbox.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtWidgets/qlabel.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtWidgets/qlayout.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtWidgets/qlayoutitem.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtWidgets/qlistview.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtWidgets/qlistwidget.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtWidgets/qmessagebox.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtWidgets/qprogressbar.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtWidgets/qpushbutton.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtWidgets/qrubberband.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtWidgets/qsizepolicy.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtWidgets/qslider.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtWidgets/qstyle.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtWidgets/qstyleoption.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtWidgets/qtabbar.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtWidgets/qtabwidget.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtWidgets/qtwidgets-config.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtWidgets/qtwidgetsglobal.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /opt/qt515/include/QtWidgets/qwidget.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: VideoSRLiteGUI_autogen/include/FileListWidget.moc

CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: ../src/UI/ImagePreviewWidget.cpp
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: ../src/UI/ImagePreviewWidget.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtCore/QDateTime
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtCore/QDebug
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtCore/QSize
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtCore/QStandardPaths
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtCore/QTimer
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtCore/qabstractitemmodel.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtCore/qalgorithms.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtCore/qarraydata.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtCore/qatomic.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtCore/qatomic_bootstrap.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtCore/qatomic_cxx11.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtCore/qatomic_msvc.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtCore/qbasicatomic.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtCore/qbasictimer.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtCore/qbytearray.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtCore/qbytearraylist.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtCore/qchar.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtCore/qcompilerdetection.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtCore/qconfig-bootstrapped.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtCore/qconfig.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtCore/qcontainerfwd.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtCore/qcontainertools_impl.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtCore/qcontiguouscache.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtCore/qcoreevent.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtCore/qdatastream.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtCore/qdatetime.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtCore/qdebug.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtCore/qdir.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtCore/qfile.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtCore/qfiledevice.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtCore/qfileinfo.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtCore/qflags.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtCore/qgenericatomic.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtCore/qglobal.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtCore/qglobalstatic.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtCore/qhash.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtCore/qhashfunctions.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtCore/qiodevice.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtCore/qiterator.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtCore/qline.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtCore/qlist.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtCore/qlocale.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtCore/qlogging.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtCore/qmap.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtCore/qmargins.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtCore/qmetatype.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtCore/qmutex.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtCore/qnamespace.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtCore/qnumeric.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtCore/qobject.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtCore/qobject_impl.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtCore/qobjectdefs.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtCore/qobjectdefs_impl.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtCore/qpair.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtCore/qpoint.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtCore/qprocessordetection.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtCore/qrect.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtCore/qrefcount.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtCore/qregexp.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtCore/qregularexpression.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtCore/qscopedpointer.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtCore/qset.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtCore/qshareddata.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtCore/qsharedpointer.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtCore/qsharedpointer_impl.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtCore/qsize.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtCore/qstandardpaths.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtCore/qstring.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtCore/qstringalgorithms.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtCore/qstringbuilder.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtCore/qstringlist.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtCore/qstringliteral.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtCore/qstringmatcher.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtCore/qstringview.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtCore/qsysinfo.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtCore/qsystemdetection.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtCore/qtcore-config.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtCore/qtextstream.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtCore/qtimer.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtCore/qtypeinfo.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtCore/qurl.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtCore/qurlquery.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtCore/qvariant.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtCore/qvarlengtharray.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtCore/qvector.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtCore/qversiontagging.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtGui/QPixmap
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtGui/qbrush.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtGui/qcolor.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtGui/qcursor.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtGui/qevent.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtGui/qfont.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtGui/qfontinfo.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtGui/qfontmetrics.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtGui/qicon.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtGui/qimage.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtGui/qkeysequence.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtGui/qmatrix.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtGui/qpaintdevice.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtGui/qpalette.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtGui/qpixelformat.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtGui/qpixmap.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtGui/qpolygon.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtGui/qregion.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtGui/qrgb.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtGui/qrgba64.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtGui/qtgui-config.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtGui/qtguiglobal.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtGui/qtouchdevice.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtGui/qtransform.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtGui/qvalidator.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtGui/qvector2d.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtGui/qwindowdefs.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtGui/qwindowdefs_win.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtWidgets/QComboBox
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtWidgets/QFileDialog
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtWidgets/QGroupBox
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtWidgets/QHBoxLayout
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtWidgets/QLabel
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtWidgets/QMessageBox
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtWidgets/QPushButton
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtWidgets/QScrollArea
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtWidgets/QScrollBar
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtWidgets/QStackedWidget
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtWidgets/QVBoxLayout
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtWidgets/QWidget
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtWidgets/qabstractbutton.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtWidgets/qabstractitemdelegate.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtWidgets/qabstractscrollarea.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtWidgets/qabstractslider.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtWidgets/qabstractspinbox.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtWidgets/qboxlayout.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtWidgets/qcombobox.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtWidgets/qdialog.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtWidgets/qfiledialog.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtWidgets/qframe.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtWidgets/qgridlayout.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtWidgets/qgroupbox.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtWidgets/qlabel.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtWidgets/qlayout.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtWidgets/qlayoutitem.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtWidgets/qmessagebox.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtWidgets/qpushbutton.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtWidgets/qrubberband.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtWidgets/qscrollarea.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtWidgets/qscrollbar.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtWidgets/qsizepolicy.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtWidgets/qslider.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtWidgets/qstackedwidget.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtWidgets/qstyle.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtWidgets/qstyleoption.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtWidgets/qtabbar.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtWidgets/qtabwidget.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtWidgets/qtwidgets-config.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtWidgets/qtwidgetsglobal.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /opt/qt515/include/QtWidgets/qwidget.h

CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: ../onnx/onnxruntime-linux-x64-1.15.1/include/onnxruntime_c_api.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: ../onnx/onnxruntime-linux-x64-1.15.1/include/onnxruntime_cxx_api.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: ../onnx/onnxruntime-linux-x64-1.15.1/include/onnxruntime_cxx_inline.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: ../src/DataStruct/FrameData.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: ../src/SuperEigen/include/ModelSession.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: ../src/SuperEigen/include/PrePostProcessor.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: ../src/SuperEigen/include/SuperResConfig.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: ../src/SuperEigen/include/SuperResEngine.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: ../src/UI/ImageProcessor.cpp
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: ../src/UI/ImageProcessor.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtCore/QAtomicInt
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtCore/QDeadlineTimer
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtCore/QDebug
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtCore/QElapsedTimer
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtCore/QMutex
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtCore/QObject
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtCore/QQueue
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtCore/QRunnable
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtCore/QThread
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtCore/QThreadPool
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtCore/QWaitCondition
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtCore/qalgorithms.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtCore/qarraydata.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtCore/qatomic.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtCore/qatomic_bootstrap.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtCore/qatomic_cxx11.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtCore/qatomic_msvc.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtCore/qbasicatomic.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtCore/qbytearray.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtCore/qbytearraylist.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtCore/qchar.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtCore/qcompilerdetection.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtCore/qconfig-bootstrapped.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtCore/qconfig.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtCore/qcontainerfwd.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtCore/qcontainertools_impl.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtCore/qcontiguouscache.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtCore/qcoreapplication.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtCore/qcoreevent.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtCore/qdatastream.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtCore/qdeadlinetimer.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtCore/qdebug.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtCore/qelapsedtimer.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtCore/qeventloop.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtCore/qfile.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtCore/qfiledevice.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtCore/qflags.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtCore/qgenericatomic.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtCore/qglobal.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtCore/qglobalstatic.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtCore/qhash.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtCore/qhashfunctions.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtCore/qiodevice.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtCore/qiterator.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtCore/qline.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtCore/qlist.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtCore/qlocale.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtCore/qlogging.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtCore/qmap.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtCore/qmargins.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtCore/qmetatype.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtCore/qmutex.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtCore/qnamespace.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtCore/qnumeric.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtCore/qobject.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtCore/qobject_impl.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtCore/qobjectdefs.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtCore/qobjectdefs_impl.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtCore/qpair.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtCore/qpoint.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtCore/qprocessordetection.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtCore/qqueue.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtCore/qrect.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtCore/qrefcount.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtCore/qregexp.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtCore/qrunnable.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtCore/qscopedpointer.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtCore/qset.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtCore/qshareddata.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtCore/qsharedpointer.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtCore/qsharedpointer_impl.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtCore/qsize.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtCore/qstring.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtCore/qstringalgorithms.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtCore/qstringbuilder.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtCore/qstringlist.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtCore/qstringliteral.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtCore/qstringmatcher.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtCore/qstringview.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtCore/qsysinfo.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtCore/qsystemdetection.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtCore/qtcore-config.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtCore/qtextstream.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtCore/qthread.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtCore/qthreadpool.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtCore/qtypeinfo.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtCore/qurl.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtCore/qurlquery.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtCore/qvariant.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtCore/qvarlengtharray.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtCore/qvector.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtCore/qversiontagging.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtCore/qwaitcondition.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtGui/qbrush.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtGui/qcolor.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtGui/qcursor.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtGui/qevent.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtGui/qfont.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtGui/qfontinfo.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtGui/qfontmetrics.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtGui/qguiapplication.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtGui/qimage.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtGui/qinputmethod.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtGui/qkeysequence.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtGui/qmatrix.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtGui/qpaintdevice.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtGui/qpalette.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtGui/qpixelformat.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtGui/qpixmap.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtGui/qpolygon.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtGui/qregion.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtGui/qrgb.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtGui/qrgba64.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtGui/qtgui-config.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtGui/qtguiglobal.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtGui/qtouchdevice.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtGui/qtransform.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtGui/qvector2d.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtGui/qwindowdefs.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtGui/qwindowdefs_win.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtWidgets/QApplication
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtWidgets/qapplication.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtWidgets/qdesktopwidget.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtWidgets/qsizepolicy.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtWidgets/qtwidgets-config.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtWidgets/qtwidgetsglobal.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /opt/qt515/include/QtWidgets/qwidget.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: VideoSRLiteGUI_autogen/include/ImageProcessor.moc

CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: ../onnx/onnxruntime-linux-x64-1.15.1/include/onnxruntime_c_api.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: ../onnx/onnxruntime-linux-x64-1.15.1/include/onnxruntime_cxx_api.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: ../onnx/onnxruntime-linux-x64-1.15.1/include/onnxruntime_cxx_inline.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: ../src/DataStruct/FrameData.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: ../src/SuperEigen/include/ModelSession.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: ../src/SuperEigen/include/PrePostProcessor.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: ../src/SuperEigen/include/SuperResConfig.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: ../src/SuperEigen/include/SuperResEngine.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: ../src/UI/FileListWidget.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: ../src/UI/ImagePreviewWidget.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: ../src/UI/ImageProcessor.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: ../src/UI/MainWindow.cpp
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: ../src/UI/MainWindow.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: ../src/UI/SettingsPanel.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: ../src/WorkerPool/WorkerPool.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/QAtomicInt
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/QDateTime
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/QDeadlineTimer
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/QDebug
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/QDir
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/QElapsedTimer
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/QFileInfo
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/QMap
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/QMimeData
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/QMimeDatabase
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/QMutex
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/QObject
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/QQueue
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/QRunnable
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/QSize
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/QStandardPaths
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/QStringList
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/QThread
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/QThreadPool
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/QTimer
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/QUrl
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/QUuid
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/QVariant
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/QWaitCondition
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qabstractitemmodel.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qalgorithms.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qarraydata.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qatomic.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qatomic_bootstrap.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qatomic_cxx11.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qatomic_msvc.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qbasicatomic.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qbasictimer.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qbytearray.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qbytearraylist.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qchar.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qcompilerdetection.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qconfig-bootstrapped.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qconfig.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qcontainerfwd.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qcontainertools_impl.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qcontiguouscache.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qcoreapplication.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qcoreevent.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qdatastream.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qdatetime.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qdeadlinetimer.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qdebug.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qdir.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qelapsedtimer.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qeventloop.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qfile.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qfiledevice.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qfileinfo.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qflags.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qgenericatomic.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qglobal.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qglobalstatic.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qhash.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qhashfunctions.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qiodevice.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qitemselectionmodel.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qiterator.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qline.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qlist.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qlocale.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qlogging.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qmap.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qmargins.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qmetatype.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qmimedata.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qmimedatabase.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qmimetype.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qmutex.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qnamespace.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qnumeric.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qobject.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qobject_impl.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qobjectdefs.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qobjectdefs_impl.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qpair.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qpoint.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qprocessordetection.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qqueue.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qrect.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qrefcount.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qregexp.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qregularexpression.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qrunnable.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qscopedpointer.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qset.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qshareddata.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qsharedpointer.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qsharedpointer_impl.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qsize.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qstandardpaths.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qstring.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qstringalgorithms.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qstringbuilder.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qstringlist.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qstringliteral.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qstringmatcher.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qstringview.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qsysinfo.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qsystemdetection.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qtcore-config.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qtextstream.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qthread.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qthreadpool.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qtimer.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qtypeinfo.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qurl.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qurlquery.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/quuid.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qvariant.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qvarlengtharray.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qvector.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qversiontagging.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtCore/qwaitcondition.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtGui/QCloseEvent
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtGui/QDragEnterEvent
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtGui/QDropEvent
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtGui/QKeyEvent
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtGui/QPainter
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtGui/QPixmap
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtGui/qbrush.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtGui/qcolor.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtGui/qcursor.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtGui/qevent.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtGui/qfont.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtGui/qfontinfo.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtGui/qfontmetrics.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtGui/qguiapplication.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtGui/qicon.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtGui/qimage.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtGui/qinputmethod.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtGui/qkeysequence.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtGui/qmatrix.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtGui/qpaintdevice.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtGui/qpainter.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtGui/qpalette.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtGui/qpen.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtGui/qpixelformat.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtGui/qpixmap.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtGui/qpolygon.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtGui/qregion.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtGui/qrgb.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtGui/qrgba64.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtGui/qtextcursor.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtGui/qtextdocument.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtGui/qtextformat.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtGui/qtextoption.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtGui/qtgui-config.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtGui/qtguiglobal.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtGui/qtouchdevice.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtGui/qtransform.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtGui/qvalidator.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtGui/qvector2d.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtGui/qwindowdefs.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtGui/qwindowdefs_win.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtWidgets/QAction
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtWidgets/QApplication
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtWidgets/QCheckBox
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtWidgets/QComboBox
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtWidgets/QDoubleSpinBox
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtWidgets/QFileDialog
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtWidgets/QGridLayout
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtWidgets/QGroupBox
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtWidgets/QHBoxLayout
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtWidgets/QLabel
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtWidgets/QLineEdit
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtWidgets/QListWidget
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtWidgets/QListWidgetItem
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtWidgets/QMainWindow
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtWidgets/QMenu
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtWidgets/QMenuBar
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtWidgets/QMessageBox
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtWidgets/QProgressBar
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtWidgets/QPushButton
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtWidgets/QRadioButton
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtWidgets/QScrollArea
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtWidgets/QScrollBar
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtWidgets/QSlider
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtWidgets/QSpacerItem
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtWidgets/QSpinBox
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtWidgets/QSplitter
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtWidgets/QStackedWidget
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtWidgets/QStatusBar
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtWidgets/QTextEdit
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtWidgets/QVBoxLayout
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtWidgets/QWidget
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtWidgets/qabstractbutton.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtWidgets/qabstractitemdelegate.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtWidgets/qabstractitemview.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtWidgets/qabstractscrollarea.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtWidgets/qabstractslider.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtWidgets/qabstractspinbox.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtWidgets/qaction.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtWidgets/qactiongroup.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtWidgets/qapplication.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtWidgets/qboxlayout.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtWidgets/qcheckbox.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtWidgets/qcombobox.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtWidgets/qdesktopwidget.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtWidgets/qdialog.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtWidgets/qfiledialog.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtWidgets/qframe.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtWidgets/qgridlayout.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtWidgets/qgroupbox.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtWidgets/qlabel.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtWidgets/qlayout.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtWidgets/qlayoutitem.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtWidgets/qlineedit.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtWidgets/qlistview.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtWidgets/qlistwidget.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtWidgets/qmainwindow.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtWidgets/qmenu.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtWidgets/qmenubar.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtWidgets/qmessagebox.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtWidgets/qprogressbar.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtWidgets/qpushbutton.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtWidgets/qradiobutton.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtWidgets/qrubberband.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtWidgets/qscrollarea.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtWidgets/qscrollbar.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtWidgets/qsizepolicy.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtWidgets/qslider.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtWidgets/qspinbox.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtWidgets/qsplitter.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtWidgets/qstackedwidget.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtWidgets/qstatusbar.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtWidgets/qstyle.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtWidgets/qstyleoption.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtWidgets/qtabbar.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtWidgets/qtabwidget.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtWidgets/qtextedit.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtWidgets/qtwidgets-config.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtWidgets/qtwidgetsglobal.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /opt/qt515/include/QtWidgets/qwidget.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: VideoSRLiteGUI_autogen/include/ui_MainWindow.h

CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: ../src/UI/SettingsPanel.cpp
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: ../src/UI/SettingsPanel.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtCore/QDebug
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtCore/QDir
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtCore/QSettings
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtCore/QStandardPaths
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtCore/qabstractitemmodel.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtCore/qalgorithms.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtCore/qarraydata.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtCore/qatomic.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtCore/qatomic_bootstrap.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtCore/qatomic_cxx11.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtCore/qatomic_msvc.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtCore/qbasicatomic.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtCore/qbytearray.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtCore/qbytearraylist.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtCore/qchar.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtCore/qcompilerdetection.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtCore/qconfig-bootstrapped.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtCore/qconfig.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtCore/qcontainerfwd.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtCore/qcontainertools_impl.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtCore/qcontiguouscache.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtCore/qcoreevent.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtCore/qdatastream.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtCore/qdebug.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtCore/qdir.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtCore/qfile.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtCore/qfiledevice.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtCore/qfileinfo.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtCore/qflags.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtCore/qgenericatomic.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtCore/qglobal.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtCore/qglobalstatic.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtCore/qhash.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtCore/qhashfunctions.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtCore/qiodevice.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtCore/qiterator.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtCore/qline.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtCore/qlist.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtCore/qlocale.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtCore/qlogging.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtCore/qmap.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtCore/qmargins.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtCore/qmetatype.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtCore/qmutex.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtCore/qnamespace.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtCore/qnumeric.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtCore/qobject.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtCore/qobject_impl.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtCore/qobjectdefs.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtCore/qobjectdefs_impl.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtCore/qpair.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtCore/qpoint.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtCore/qprocessordetection.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtCore/qrect.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtCore/qrefcount.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtCore/qregexp.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtCore/qregularexpression.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtCore/qscopedpointer.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtCore/qset.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtCore/qsettings.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtCore/qshareddata.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtCore/qsharedpointer.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtCore/qsharedpointer_impl.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtCore/qsize.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtCore/qstandardpaths.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtCore/qstring.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtCore/qstringalgorithms.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtCore/qstringbuilder.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtCore/qstringlist.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtCore/qstringliteral.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtCore/qstringmatcher.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtCore/qstringview.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtCore/qsysinfo.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtCore/qsystemdetection.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtCore/qtcore-config.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtCore/qtextstream.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtCore/qtypeinfo.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtCore/qurl.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtCore/qurlquery.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtCore/qvariant.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtCore/qvarlengtharray.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtCore/qvector.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtCore/qversiontagging.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtGui/qbrush.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtGui/qcolor.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtGui/qcursor.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtGui/qevent.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtGui/qfont.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtGui/qfontinfo.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtGui/qfontmetrics.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtGui/qicon.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtGui/qimage.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtGui/qkeysequence.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtGui/qmatrix.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtGui/qpaintdevice.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtGui/qpalette.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtGui/qpen.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtGui/qpixelformat.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtGui/qpixmap.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtGui/qpolygon.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtGui/qregion.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtGui/qrgb.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtGui/qrgba64.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtGui/qtextcursor.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtGui/qtextformat.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtGui/qtextoption.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtGui/qtgui-config.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtGui/qtguiglobal.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtGui/qtouchdevice.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtGui/qtransform.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtGui/qvalidator.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtGui/qvector2d.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtGui/qwindowdefs.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtGui/qwindowdefs_win.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtWidgets/QCheckBox
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtWidgets/QComboBox
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtWidgets/QDoubleSpinBox
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtWidgets/QFileDialog
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtWidgets/QGridLayout
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtWidgets/QGroupBox
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtWidgets/QHBoxLayout
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtWidgets/QInputDialog
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtWidgets/QLabel
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtWidgets/QLineEdit
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtWidgets/QMessageBox
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtWidgets/QPushButton
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtWidgets/QSlider
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtWidgets/QSpinBox
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtWidgets/QVBoxLayout
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtWidgets/QWidget
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtWidgets/qabstractbutton.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtWidgets/qabstractitemdelegate.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtWidgets/qabstractslider.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtWidgets/qabstractspinbox.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtWidgets/qboxlayout.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtWidgets/qcheckbox.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtWidgets/qcombobox.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtWidgets/qdialog.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtWidgets/qfiledialog.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtWidgets/qframe.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtWidgets/qgridlayout.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtWidgets/qgroupbox.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtWidgets/qinputdialog.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtWidgets/qlabel.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtWidgets/qlayout.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtWidgets/qlayoutitem.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtWidgets/qlineedit.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtWidgets/qmessagebox.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtWidgets/qpushbutton.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtWidgets/qrubberband.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtWidgets/qsizepolicy.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtWidgets/qslider.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtWidgets/qspinbox.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtWidgets/qstyle.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtWidgets/qstyleoption.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtWidgets/qtabbar.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtWidgets/qtabwidget.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtWidgets/qtwidgets-config.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtWidgets/qtwidgetsglobal.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /opt/qt515/include/QtWidgets/qwidget.h

CMakeFiles/VideoSRLiteGUI.dir/src/Utils/FileUtils.cpp.o: ../src/Utils/FileUtils.cpp
CMakeFiles/VideoSRLiteGUI.dir/src/Utils/FileUtils.cpp.o: ../src/Utils/FileUtils.h

CMakeFiles/VideoSRLiteGUI.dir/src/Utils/LogUtils.cpp.o: ../src/Utils/LogUtils.cpp
CMakeFiles/VideoSRLiteGUI.dir/src/Utils/LogUtils.cpp.o: ../src/Utils/LogUtils.h

CMakeFiles/VideoSRLiteGUI.dir/src/Utils/Logger.cpp.o: ../src/Utils/Logger.cpp
CMakeFiles/VideoSRLiteGUI.dir/src/Utils/Logger.cpp.o: ../src/Utils/Logger.h

CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: ../src/WorkerPool/WorkerPool.cpp
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: ../src/WorkerPool/WorkerPool.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /opt/qt515/include/QtCore/QDeadlineTimer
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /opt/qt515/include/QtCore/QDebug
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /opt/qt515/include/QtCore/QMutex
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /opt/qt515/include/QtCore/QObject
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /opt/qt515/include/QtCore/QQueue
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /opt/qt515/include/QtCore/QThread
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /opt/qt515/include/QtCore/QWaitCondition
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /opt/qt515/include/QtCore/qalgorithms.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /opt/qt515/include/QtCore/qarraydata.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /opt/qt515/include/QtCore/qatomic.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /opt/qt515/include/QtCore/qatomic_bootstrap.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /opt/qt515/include/QtCore/qatomic_cxx11.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /opt/qt515/include/QtCore/qatomic_msvc.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /opt/qt515/include/QtCore/qbasicatomic.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /opt/qt515/include/QtCore/qbytearray.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /opt/qt515/include/QtCore/qbytearraylist.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /opt/qt515/include/QtCore/qchar.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /opt/qt515/include/QtCore/qcompilerdetection.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /opt/qt515/include/QtCore/qconfig-bootstrapped.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /opt/qt515/include/QtCore/qconfig.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /opt/qt515/include/QtCore/qcontainerfwd.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /opt/qt515/include/QtCore/qcontainertools_impl.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /opt/qt515/include/QtCore/qcontiguouscache.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /opt/qt515/include/QtCore/qcoreevent.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /opt/qt515/include/QtCore/qdeadlinetimer.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /opt/qt515/include/QtCore/qdebug.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /opt/qt515/include/QtCore/qelapsedtimer.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /opt/qt515/include/QtCore/qflags.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /opt/qt515/include/QtCore/qgenericatomic.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /opt/qt515/include/QtCore/qglobal.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /opt/qt515/include/QtCore/qglobalstatic.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /opt/qt515/include/QtCore/qhash.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /opt/qt515/include/QtCore/qhashfunctions.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /opt/qt515/include/QtCore/qiodevice.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /opt/qt515/include/QtCore/qiterator.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /opt/qt515/include/QtCore/qlist.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /opt/qt515/include/QtCore/qlocale.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /opt/qt515/include/QtCore/qlogging.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /opt/qt515/include/QtCore/qmap.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /opt/qt515/include/QtCore/qmetatype.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /opt/qt515/include/QtCore/qmutex.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /opt/qt515/include/QtCore/qnamespace.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /opt/qt515/include/QtCore/qnumeric.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /opt/qt515/include/QtCore/qobject.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /opt/qt515/include/QtCore/qobject_impl.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /opt/qt515/include/QtCore/qobjectdefs.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /opt/qt515/include/QtCore/qobjectdefs_impl.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /opt/qt515/include/QtCore/qpair.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /opt/qt515/include/QtCore/qpoint.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /opt/qt515/include/QtCore/qprocessordetection.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /opt/qt515/include/QtCore/qqueue.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /opt/qt515/include/QtCore/qrefcount.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /opt/qt515/include/QtCore/qregexp.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /opt/qt515/include/QtCore/qscopedpointer.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /opt/qt515/include/QtCore/qset.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /opt/qt515/include/QtCore/qshareddata.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /opt/qt515/include/QtCore/qsharedpointer.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /opt/qt515/include/QtCore/qsharedpointer_impl.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /opt/qt515/include/QtCore/qstring.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /opt/qt515/include/QtCore/qstringalgorithms.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /opt/qt515/include/QtCore/qstringbuilder.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /opt/qt515/include/QtCore/qstringlist.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /opt/qt515/include/QtCore/qstringliteral.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /opt/qt515/include/QtCore/qstringmatcher.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /opt/qt515/include/QtCore/qstringview.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /opt/qt515/include/QtCore/qsysinfo.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /opt/qt515/include/QtCore/qsystemdetection.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /opt/qt515/include/QtCore/qtcore-config.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /opt/qt515/include/QtCore/qtextstream.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /opt/qt515/include/QtCore/qthread.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /opt/qt515/include/QtCore/qtypeinfo.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /opt/qt515/include/QtCore/qvariant.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /opt/qt515/include/QtCore/qvarlengtharray.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /opt/qt515/include/QtCore/qvector.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /opt/qt515/include/QtCore/qversiontagging.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /opt/qt515/include/QtCore/qwaitcondition.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: VideoSRLiteGUI_autogen/include/WorkerPool.moc

CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: ../onnx/onnxruntime-linux-x64-1.15.1/include/onnxruntime_c_api.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: ../onnx/onnxruntime-linux-x64-1.15.1/include/onnxruntime_cxx_api.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: ../onnx/onnxruntime-linux-x64-1.15.1/include/onnxruntime_cxx_inline.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: ../src/DataStruct/FrameData.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: ../src/SuperEigen/include/ModelSession.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: ../src/SuperEigen/include/PrePostProcessor.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: ../src/SuperEigen/include/SuperResConfig.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: ../src/SuperEigen/include/SuperResEngine.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: ../src/UI/FileListWidget.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: ../src/UI/ImagePreviewWidget.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: ../src/UI/ImageProcessor.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: ../src/UI/MainWindow.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: ../src/UI/SettingsPanel.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: ../src/WorkerPool/WorkerPool.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: ../src/main.cpp
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/QAtomicInt
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/QDateTime
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/QDeadlineTimer
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/QElapsedTimer
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/QFileInfo
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/QMap
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/QMetaType
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/QMimeData
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/QMutex
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/QObject
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/QQueue
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/QRunnable
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/QSize
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/QStandardPaths
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/QStringList
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/QThread
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/QThreadPool
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/QTimer
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/QUrl
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/QWaitCondition
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/qabstractitemmodel.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/qalgorithms.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/qarraydata.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/qatomic.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/qatomic_bootstrap.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/qatomic_cxx11.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/qatomic_msvc.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/qbasicatomic.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/qbasictimer.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/qbytearray.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/qbytearraylist.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/qchar.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/qcompilerdetection.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/qconfig-bootstrapped.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/qconfig.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/qcontainerfwd.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/qcontainertools_impl.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/qcontiguouscache.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/qcoreapplication.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/qcoreevent.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/qdatastream.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/qdatetime.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/qdeadlinetimer.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/qdebug.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/qdir.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/qelapsedtimer.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/qeventloop.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/qfile.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/qfiledevice.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/qfileinfo.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/qflags.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/qgenericatomic.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/qglobal.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/qglobalstatic.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/qhash.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/qhashfunctions.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/qiodevice.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/qitemselectionmodel.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/qiterator.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/qline.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/qlist.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/qlocale.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/qlogging.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/qmap.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/qmargins.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/qmetatype.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/qmimedata.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/qmutex.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/qnamespace.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/qnumeric.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/qobject.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/qobject_impl.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/qobjectdefs.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/qobjectdefs_impl.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/qpair.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/qpoint.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/qprocessordetection.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/qqueue.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/qrect.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/qrefcount.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/qregexp.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/qregularexpression.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/qrunnable.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/qscopedpointer.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/qset.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/qshareddata.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/qsharedpointer.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/qsharedpointer_impl.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/qsize.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/qstandardpaths.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/qstring.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/qstringalgorithms.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/qstringbuilder.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/qstringlist.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/qstringliteral.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/qstringmatcher.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/qstringview.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/qsysinfo.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/qsystemdetection.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/qtcore-config.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/qtextstream.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/qthread.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/qthreadpool.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/qtimer.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/qtypeinfo.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/qurl.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/qurlquery.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/qvariant.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/qvarlengtharray.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/qvector.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/qversiontagging.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtCore/qwaitcondition.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtGui/QCloseEvent
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtGui/QDragEnterEvent
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtGui/QDropEvent
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtGui/QPixmap
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtGui/qbrush.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtGui/qcolor.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtGui/qcursor.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtGui/qevent.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtGui/qfont.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtGui/qfontinfo.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtGui/qfontmetrics.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtGui/qguiapplication.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtGui/qicon.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtGui/qimage.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtGui/qinputmethod.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtGui/qkeysequence.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtGui/qmatrix.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtGui/qpaintdevice.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtGui/qpalette.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtGui/qpen.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtGui/qpixelformat.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtGui/qpixmap.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtGui/qpolygon.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtGui/qregion.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtGui/qrgb.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtGui/qrgba64.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtGui/qtextcursor.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtGui/qtextdocument.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtGui/qtextformat.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtGui/qtextoption.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtGui/qtgui-config.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtGui/qtguiglobal.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtGui/qtouchdevice.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtGui/qtransform.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtGui/qvalidator.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtGui/qvector2d.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtGui/qwindowdefs.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtGui/qwindowdefs_win.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtWidgets/QApplication
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtWidgets/QCheckBox
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtWidgets/QComboBox
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtWidgets/QDoubleSpinBox
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtWidgets/QFileDialog
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtWidgets/QGridLayout
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtWidgets/QGroupBox
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtWidgets/QHBoxLayout
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtWidgets/QLabel
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtWidgets/QLineEdit
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtWidgets/QListWidget
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtWidgets/QListWidgetItem
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtWidgets/QMainWindow
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtWidgets/QMessageBox
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtWidgets/QProgressBar
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtWidgets/QPushButton
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtWidgets/QScrollArea
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtWidgets/QScrollBar
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtWidgets/QSlider
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtWidgets/QSpinBox
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtWidgets/QSplitter
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtWidgets/QStackedWidget
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtWidgets/QTextEdit
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtWidgets/QVBoxLayout
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtWidgets/QWidget
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtWidgets/qabstractbutton.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtWidgets/qabstractitemdelegate.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtWidgets/qabstractitemview.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtWidgets/qabstractscrollarea.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtWidgets/qabstractslider.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtWidgets/qabstractspinbox.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtWidgets/qapplication.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtWidgets/qboxlayout.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtWidgets/qcheckbox.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtWidgets/qcombobox.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtWidgets/qdesktopwidget.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtWidgets/qdialog.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtWidgets/qfiledialog.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtWidgets/qframe.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtWidgets/qgridlayout.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtWidgets/qgroupbox.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtWidgets/qlabel.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtWidgets/qlayout.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtWidgets/qlayoutitem.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtWidgets/qlineedit.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtWidgets/qlistview.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtWidgets/qlistwidget.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtWidgets/qmainwindow.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtWidgets/qmessagebox.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtWidgets/qprogressbar.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtWidgets/qpushbutton.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtWidgets/qrubberband.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtWidgets/qscrollarea.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtWidgets/qscrollbar.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtWidgets/qsizepolicy.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtWidgets/qslider.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtWidgets/qspinbox.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtWidgets/qsplitter.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtWidgets/qstackedwidget.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtWidgets/qstyle.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtWidgets/qstyleoption.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtWidgets/qtabbar.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtWidgets/qtabwidget.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtWidgets/qtextedit.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtWidgets/qtwidgets-config.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtWidgets/qtwidgetsglobal.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /opt/qt515/include/QtWidgets/qwidget.h

