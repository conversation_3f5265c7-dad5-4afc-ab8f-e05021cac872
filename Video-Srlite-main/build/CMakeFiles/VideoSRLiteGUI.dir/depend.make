# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.10

CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: ../onnx/onnxruntime-linux-x64-1.15.1/include/onnxruntime_c_api.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: ../onnx/onnxruntime-linux-x64-1.15.1/include/onnxruntime_cxx_api.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: ../onnx/onnxruntime-linux-x64-1.15.1/include/onnxruntime_cxx_inline.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: VideoSRLiteGUI_autogen/HC4UDM2PON/moc_WorkerPool.cpp
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: VideoSRLiteGUI_autogen/WAYUIA5GRM/moc_FileListWidget.cpp
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: VideoSRLiteGUI_autogen/WAYUIA5GRM/moc_ImagePreviewWidget.cpp
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: VideoSRLiteGUI_autogen/WAYUIA5GRM/moc_ImageProcessor.cpp
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: VideoSRLiteGUI_autogen/WAYUIA5GRM/moc_MainWindow.cpp
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: VideoSRLiteGUI_autogen/WAYUIA5GRM/moc_SettingsPanel.cpp
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: VideoSRLiteGUI_autogen/mocs_compilation.cpp
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: ../src/DataStruct/FrameData.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: ../src/SuperEigen/include/ModelSession.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: ../src/SuperEigen/include/PrePostProcessor.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: ../src/SuperEigen/include/SuperResConfig.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: ../src/SuperEigen/include/SuperResEngine.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: ../src/UI/FileListWidget.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: ../src/UI/ImagePreviewWidget.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: ../src/UI/ImageProcessor.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: ../src/UI/MainWindow.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: ../src/UI/SettingsPanel.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: ../src/WorkerPool/WorkerPool.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/QAtomicInt
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/QDateTime
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/QElapsedTimer
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/QFileInfo
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/QMap
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/QMimeData
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/QMutex
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/QObject
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/QQueue
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/QRunnable
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/QSize
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/QStandardPaths
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/QStringList
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/QThread
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/QThreadPool
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/QTimer
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/QUrl
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/QWaitCondition
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qabstractitemmodel.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qalgorithms.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qarraydata.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qatomic.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qatomic_bootstrap.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qatomic_cxx11.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qatomic_msvc.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qbasicatomic.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qbasictimer.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qbytearray.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qbytearraylist.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qchar.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qcompilerdetection.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qconfig-bootstrapped.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qconfig.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qcontainerfwd.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qcontiguouscache.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qcoreapplication.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qcoreevent.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qdatastream.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qdatetime.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qdebug.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qdir.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qelapsedtimer.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qeventloop.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qfile.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qfiledevice.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qfileinfo.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qflags.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qgenericatomic.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qglobal.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qglobalstatic.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qhash.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qhashfunctions.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qiodevice.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qitemselectionmodel.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qiterator.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qline.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qlist.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qlocale.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qlogging.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qmap.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qmargins.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qmetatype.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qmimedata.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qmutex.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qnamespace.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qnumeric.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qobject.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qobject_impl.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qobjectdefs.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qobjectdefs_impl.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qpair.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qpoint.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qprocessordetection.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qqueue.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qrect.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qrefcount.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qregexp.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qregularexpression.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qrunnable.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qscopedpointer.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qset.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qshareddata.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qsharedpointer.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qsharedpointer_impl.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qsize.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qstandardpaths.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qstring.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qstringbuilder.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qstringlist.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qstringmatcher.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qsysinfo.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qsystemdetection.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qtcore-config.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qtextstream.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qthread.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qthreadpool.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qtimer.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qtypeinfo.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qurl.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qurlquery.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qvariant.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qvarlengtharray.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qvector.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qversiontagging.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qwaitcondition.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/QCloseEvent
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/QDragEnterEvent
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/QDropEvent
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/QPixmap
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qbrush.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qcolor.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qcursor.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qevent.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qfont.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qfontinfo.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qfontmetrics.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qguiapplication.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qicon.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qimage.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qinputmethod.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qkeysequence.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qmatrix.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qpaintdevice.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qpainterpath.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qpalette.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qpen.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qpixelformat.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qpixmap.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qpolygon.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qregion.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qrgb.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qrgba64.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qtextcursor.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qtextdocument.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qtextformat.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qtextoption.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qtgui-config.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qtguiglobal.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qtouchdevice.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qtransform.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qvalidator.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qvector2d.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qwindowdefs.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qwindowdefs_win.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QApplication
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QCheckBox
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QComboBox
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QDoubleSpinBox
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QFileDialog
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QGridLayout
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QGroupBox
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QHBoxLayout
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QLabel
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QLineEdit
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QListWidget
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QListWidgetItem
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QMainWindow
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QMessageBox
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QProgressBar
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QPushButton
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QScrollArea
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QScrollBar
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QSlider
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QSpinBox
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QSplitter
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QStackedWidget
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QTextEdit
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QVBoxLayout
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QWidget
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qabstractbutton.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qabstractitemdelegate.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qabstractitemview.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qabstractscrollarea.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qabstractslider.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qabstractspinbox.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qapplication.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qboxlayout.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qcheckbox.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qcombobox.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qdesktopwidget.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qdialog.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qfiledialog.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qframe.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qgridlayout.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qgroupbox.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qlabel.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qlayout.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qlayoutitem.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qlineedit.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qlistview.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qlistwidget.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qmainwindow.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qmessagebox.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qprogressbar.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qpushbutton.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qrubberband.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qscrollarea.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qscrollbar.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qsizepolicy.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qslider.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qspinbox.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qsplitter.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qstackedwidget.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qstyle.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qstyleoption.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qtabbar.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qtabwidget.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qtextedit.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qtwidgets-config.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qtwidgetsglobal.h
CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qwidget.h

CMakeFiles/VideoSRLiteGUI.dir/src/AppController/AppController.cpp.o: ../src/AppController/AppController.cpp

CMakeFiles/VideoSRLiteGUI.dir/src/AppController/WorkerPool.cpp.o: ../src/AppController/WorkerPool.cpp

CMakeFiles/VideoSRLiteGUI.dir/src/AudioProc/AudioProc.cpp.o: ../src/AudioProc/AudioProc.cpp

CMakeFiles/VideoSRLiteGUI.dir/src/AudioProcessor/AudioProcessor.cpp.o: ../src/AudioProcessor/AudioProcessor.cpp

CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/AudioDecoder.cpp.o: ../src/DataStruct/AudioFrameData.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/AudioDecoder.cpp.o: ../src/Decoder/include/AudioDecoder.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/AudioDecoder.cpp.o: ../src/Decoder/src/AudioDecoder.cpp
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/AudioDecoder.cpp.o: ../src/Utils/Logger.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/AudioDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/avcodec.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/AudioDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/bsf.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/AudioDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/codec.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/AudioDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/codec_desc.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/AudioDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/codec_id.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/AudioDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/codec_par.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/AudioDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/packet.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/AudioDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/version.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/AudioDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavformat/avformat.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/AudioDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavformat/avio.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/AudioDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavformat/version.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/AudioDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/attributes.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/AudioDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/avconfig.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/AudioDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/avutil.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/AudioDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/buffer.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/AudioDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/channel_layout.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/AudioDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/common.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/AudioDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/cpu.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/AudioDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/dict.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/AudioDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/error.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/AudioDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/frame.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/AudioDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/hwcontext.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/AudioDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/intfloat.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/AudioDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/log.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/AudioDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/macros.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/AudioDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/mathematics.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/AudioDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/mem.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/AudioDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/opt.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/AudioDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/pixfmt.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/AudioDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/rational.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/AudioDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/samplefmt.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/AudioDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/version.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/AudioDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libswresample/swresample.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/AudioDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libswresample/version.h

CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/Decoder.cpp.o: ../src/DataStruct/AudioFrameData.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/Decoder.cpp.o: ../src/DataStruct/FrameData.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/Decoder.cpp.o: ../src/Decoder/include/AudioDecoder.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/Decoder.cpp.o: ../src/Decoder/include/Decoder.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/Decoder.cpp.o: ../src/Decoder/include/VideoDecoder.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/Decoder.cpp.o: ../src/Decoder/src/Decoder.cpp
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/Decoder.cpp.o: ../src/Utils/Logger.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/Decoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/avcodec.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/Decoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/bsf.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/Decoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/codec.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/Decoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/codec_desc.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/Decoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/codec_id.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/Decoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/codec_par.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/Decoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/packet.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/Decoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/version.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/Decoder.cpp.o: /usr/include/x86_64-linux-gnu/libavformat/avformat.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/Decoder.cpp.o: /usr/include/x86_64-linux-gnu/libavformat/avio.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/Decoder.cpp.o: /usr/include/x86_64-linux-gnu/libavformat/version.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/Decoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/attributes.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/Decoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/avconfig.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/Decoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/avutil.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/Decoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/buffer.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/Decoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/channel_layout.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/Decoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/common.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/Decoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/cpu.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/Decoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/dict.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/Decoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/error.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/Decoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/frame.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/Decoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/hwcontext.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/Decoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/intfloat.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/Decoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/log.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/Decoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/macros.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/Decoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/mathematics.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/Decoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/mem.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/Decoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/pixdesc.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/Decoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/pixfmt.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/Decoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/rational.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/Decoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/samplefmt.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/Decoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/version.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/Decoder.cpp.o: /usr/include/x86_64-linux-gnu/libswresample/swresample.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/Decoder.cpp.o: /usr/include/x86_64-linux-gnu/libswresample/version.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/Decoder.cpp.o: /usr/include/x86_64-linux-gnu/libswscale/swscale.h

CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/VideoDecoder.cpp.o: ../src/DataStruct/FrameData.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/VideoDecoder.cpp.o: ../src/Decoder/include/VideoDecoder.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/VideoDecoder.cpp.o: ../src/Decoder/src/VideoDecoder.cpp
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/VideoDecoder.cpp.o: ../src/Utils/Logger.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/VideoDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/avcodec.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/VideoDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/bsf.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/VideoDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/codec.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/VideoDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/codec_desc.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/VideoDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/codec_id.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/VideoDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/codec_par.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/VideoDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/packet.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/VideoDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/version.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/VideoDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavformat/avformat.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/VideoDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavformat/avio.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/VideoDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavformat/version.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/VideoDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/attributes.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/VideoDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/avconfig.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/VideoDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/avutil.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/VideoDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/buffer.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/VideoDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/channel_layout.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/VideoDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/common.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/VideoDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/cpu.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/VideoDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/dict.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/VideoDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/error.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/VideoDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/frame.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/VideoDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/hwcontext.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/VideoDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/intfloat.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/VideoDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/log.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/VideoDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/macros.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/VideoDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/mathematics.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/VideoDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/mem.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/VideoDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/pixdesc.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/VideoDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/pixfmt.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/VideoDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/rational.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/VideoDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/samplefmt.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/VideoDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/version.h
CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/VideoDecoder.cpp.o: /usr/include/x86_64-linux-gnu/libswscale/swscale.h

CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/AudioEncoder.cpp.o: ../src/DataStruct/AudioFrameData.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/AudioEncoder.cpp.o: ../src/Encoder/AudioEncoder.cpp
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/AudioEncoder.cpp.o: ../src/Encoder/AudioEncoder.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/AudioEncoder.cpp.o: ../src/Utils/Logger.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/AudioEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/avcodec.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/AudioEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/bsf.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/AudioEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/codec.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/AudioEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/codec_desc.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/AudioEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/codec_id.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/AudioEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/codec_par.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/AudioEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/packet.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/AudioEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/version.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/AudioEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavformat/avformat.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/AudioEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavformat/avio.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/AudioEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavformat/version.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/AudioEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/attributes.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/AudioEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/avconfig.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/AudioEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/avutil.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/AudioEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/buffer.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/AudioEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/channel_layout.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/AudioEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/common.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/AudioEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/cpu.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/AudioEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/dict.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/AudioEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/error.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/AudioEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/frame.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/AudioEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/hwcontext.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/AudioEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/intfloat.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/AudioEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/log.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/AudioEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/macros.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/AudioEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/mathematics.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/AudioEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/mem.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/AudioEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/opt.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/AudioEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/pixfmt.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/AudioEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/rational.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/AudioEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/samplefmt.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/AudioEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/version.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/AudioEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libswresample/swresample.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/AudioEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libswresample/version.h

CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o: ../src/DataStruct/AudioFrameData.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o: ../src/DataStruct/FrameData.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o: ../src/Encoder/AudioEncoder.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o: ../src/Encoder/Encoder.cpp
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o: ../src/Encoder/Encoder.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o: ../src/Encoder/Muxer.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o: ../src/Encoder/VideoEncoder.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o: ../src/Utils/Logger.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/avcodec.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/bsf.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/codec.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/codec_desc.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/codec_id.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/codec_par.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/packet.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/version.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o: /usr/include/x86_64-linux-gnu/libavformat/avformat.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o: /usr/include/x86_64-linux-gnu/libavformat/avio.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o: /usr/include/x86_64-linux-gnu/libavformat/version.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/attributes.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/avconfig.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/avutil.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/buffer.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/channel_layout.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/common.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/cpu.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/dict.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/error.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/frame.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/hwcontext.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/imgutils.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/intfloat.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/log.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/macros.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/mathematics.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/mem.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/opt.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/pixdesc.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/pixfmt.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/rational.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/samplefmt.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/version.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o: /usr/include/x86_64-linux-gnu/libswresample/swresample.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o: /usr/include/x86_64-linux-gnu/libswresample/version.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o: /usr/include/x86_64-linux-gnu/libswscale/swscale.h

CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Muxer.cpp.o: ../src/Encoder/Muxer.cpp
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Muxer.cpp.o: ../src/Encoder/Muxer.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Muxer.cpp.o: ../src/Utils/Logger.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Muxer.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/avcodec.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Muxer.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/bsf.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Muxer.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/codec.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Muxer.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/codec_desc.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Muxer.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/codec_id.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Muxer.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/codec_par.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Muxer.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/packet.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Muxer.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/version.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Muxer.cpp.o: /usr/include/x86_64-linux-gnu/libavformat/avformat.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Muxer.cpp.o: /usr/include/x86_64-linux-gnu/libavformat/avio.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Muxer.cpp.o: /usr/include/x86_64-linux-gnu/libavformat/version.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Muxer.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/attributes.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Muxer.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/avconfig.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Muxer.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/avutil.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Muxer.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/buffer.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Muxer.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/channel_layout.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Muxer.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/common.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Muxer.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/cpu.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Muxer.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/dict.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Muxer.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/error.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Muxer.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/frame.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Muxer.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/hwcontext.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Muxer.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/intfloat.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Muxer.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/log.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Muxer.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/macros.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Muxer.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/mathematics.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Muxer.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/mem.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Muxer.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/pixfmt.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Muxer.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/rational.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Muxer.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/samplefmt.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Muxer.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/version.h

CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/VideoEncoder.cpp.o: ../src/DataStruct/FrameData.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/VideoEncoder.cpp.o: ../src/Encoder/VideoEncoder.cpp
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/VideoEncoder.cpp.o: ../src/Encoder/VideoEncoder.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/VideoEncoder.cpp.o: ../src/Utils/Logger.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/VideoEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/avcodec.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/VideoEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/bsf.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/VideoEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/codec.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/VideoEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/codec_desc.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/VideoEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/codec_id.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/VideoEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/codec_par.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/VideoEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/packet.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/VideoEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavcodec/version.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/VideoEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavformat/avformat.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/VideoEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavformat/avio.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/VideoEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavformat/version.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/VideoEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/attributes.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/VideoEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/avconfig.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/VideoEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/avutil.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/VideoEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/buffer.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/VideoEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/channel_layout.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/VideoEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/common.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/VideoEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/cpu.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/VideoEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/dict.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/VideoEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/error.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/VideoEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/frame.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/VideoEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/hwcontext.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/VideoEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/imgutils.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/VideoEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/intfloat.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/VideoEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/log.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/VideoEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/macros.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/VideoEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/mathematics.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/VideoEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/mem.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/VideoEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/opt.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/VideoEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/pixdesc.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/VideoEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/pixfmt.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/VideoEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/rational.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/VideoEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/samplefmt.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/VideoEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libavutil/version.h
CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/VideoEncoder.cpp.o: /usr/include/x86_64-linux-gnu/libswscale/swscale.h

CMakeFiles/VideoSRLiteGUI.dir/src/PostFilter/PostFilter.cpp.o: ../src/PostFilter/PostFilter.cpp

CMakeFiles/VideoSRLiteGUI.dir/src/PostFilter/PostFilterProcessor.cpp.o: ../src/PostFilter/PostFilterProcessor.cpp

CMakeFiles/VideoSRLiteGUI.dir/src/Processing/AudioDenoiser.cpp.o: ../src/Processing/AudioDenoiser.cpp
CMakeFiles/VideoSRLiteGUI.dir/src/Processing/AudioDenoiser.cpp.o: ../src/Processing/AudioDenoiser.h

CMakeFiles/VideoSRLiteGUI.dir/src/Processing/PostProcessor.cpp.o: ../src/Processing/PostProcessor.cpp
CMakeFiles/VideoSRLiteGUI.dir/src/Processing/PostProcessor.cpp.o: ../src/Processing/PostProcessor.h

CMakeFiles/VideoSRLiteGUI.dir/src/Processing/SuperResolution.cpp.o: ../onnx/onnxruntime-linux-x64-1.15.1/include/onnxruntime_c_api.h
CMakeFiles/VideoSRLiteGUI.dir/src/Processing/SuperResolution.cpp.o: ../onnx/onnxruntime-linux-x64-1.15.1/include/onnxruntime_cxx_api.h
CMakeFiles/VideoSRLiteGUI.dir/src/Processing/SuperResolution.cpp.o: ../onnx/onnxruntime-linux-x64-1.15.1/include/onnxruntime_cxx_inline.h
CMakeFiles/VideoSRLiteGUI.dir/src/Processing/SuperResolution.cpp.o: ../src/DataStruct/FrameData.h
CMakeFiles/VideoSRLiteGUI.dir/src/Processing/SuperResolution.cpp.o: ../src/Processing/SuperResolution.cpp
CMakeFiles/VideoSRLiteGUI.dir/src/Processing/SuperResolution.cpp.o: ../src/Processing/SuperResolution.h
CMakeFiles/VideoSRLiteGUI.dir/src/Processing/SuperResolution.cpp.o: ../src/SuperEigen/include/ModelSession.h
CMakeFiles/VideoSRLiteGUI.dir/src/Processing/SuperResolution.cpp.o: ../src/SuperEigen/include/PrePostProcessor.h
CMakeFiles/VideoSRLiteGUI.dir/src/Processing/SuperResolution.cpp.o: ../src/SuperEigen/include/SuperResConfig.h
CMakeFiles/VideoSRLiteGUI.dir/src/Processing/SuperResolution.cpp.o: ../src/SuperEigen/include/SuperResEngine.h
CMakeFiles/VideoSRLiteGUI.dir/src/Processing/SuperResolution.cpp.o: ../src/Utils/Logger.h

CMakeFiles/VideoSRLiteGUI.dir/src/SuperEigen/src/ModelSession.cpp.o: ../onnx/onnxruntime-linux-x64-1.15.1/include/onnxruntime_c_api.h
CMakeFiles/VideoSRLiteGUI.dir/src/SuperEigen/src/ModelSession.cpp.o: ../onnx/onnxruntime-linux-x64-1.15.1/include/onnxruntime_cxx_api.h
CMakeFiles/VideoSRLiteGUI.dir/src/SuperEigen/src/ModelSession.cpp.o: ../onnx/onnxruntime-linux-x64-1.15.1/include/onnxruntime_cxx_inline.h
CMakeFiles/VideoSRLiteGUI.dir/src/SuperEigen/src/ModelSession.cpp.o: ../src/SuperEigen/include/ModelSession.h
CMakeFiles/VideoSRLiteGUI.dir/src/SuperEigen/src/ModelSession.cpp.o: ../src/SuperEigen/include/SuperResConfig.h
CMakeFiles/VideoSRLiteGUI.dir/src/SuperEigen/src/ModelSession.cpp.o: ../src/SuperEigen/src/ModelSession.cpp
CMakeFiles/VideoSRLiteGUI.dir/src/SuperEigen/src/ModelSession.cpp.o: ../src/Utils/Logger.h

CMakeFiles/VideoSRLiteGUI.dir/src/SuperEigen/src/PrePostProcessor.cpp.o: ../onnx/onnxruntime-linux-x64-1.15.1/include/onnxruntime_c_api.h
CMakeFiles/VideoSRLiteGUI.dir/src/SuperEigen/src/PrePostProcessor.cpp.o: ../onnx/onnxruntime-linux-x64-1.15.1/include/onnxruntime_cxx_api.h
CMakeFiles/VideoSRLiteGUI.dir/src/SuperEigen/src/PrePostProcessor.cpp.o: ../onnx/onnxruntime-linux-x64-1.15.1/include/onnxruntime_cxx_inline.h
CMakeFiles/VideoSRLiteGUI.dir/src/SuperEigen/src/PrePostProcessor.cpp.o: ../src/SuperEigen/include/PrePostProcessor.h
CMakeFiles/VideoSRLiteGUI.dir/src/SuperEigen/src/PrePostProcessor.cpp.o: ../src/SuperEigen/include/SuperResConfig.h
CMakeFiles/VideoSRLiteGUI.dir/src/SuperEigen/src/PrePostProcessor.cpp.o: ../src/SuperEigen/src/PrePostProcessor.cpp

CMakeFiles/VideoSRLiteGUI.dir/src/SuperEigen/src/SuperResConfig.cpp.o: ../onnx/onnxruntime-linux-x64-1.15.1/include/onnxruntime_c_api.h
CMakeFiles/VideoSRLiteGUI.dir/src/SuperEigen/src/SuperResConfig.cpp.o: ../onnx/onnxruntime-linux-x64-1.15.1/include/onnxruntime_cxx_api.h
CMakeFiles/VideoSRLiteGUI.dir/src/SuperEigen/src/SuperResConfig.cpp.o: ../onnx/onnxruntime-linux-x64-1.15.1/include/onnxruntime_cxx_inline.h
CMakeFiles/VideoSRLiteGUI.dir/src/SuperEigen/src/SuperResConfig.cpp.o: ../src/SuperEigen/include/SuperResConfig.h
CMakeFiles/VideoSRLiteGUI.dir/src/SuperEigen/src/SuperResConfig.cpp.o: ../src/SuperEigen/src/SuperResConfig.cpp

CMakeFiles/VideoSRLiteGUI.dir/src/SuperEigen/src/SuperResEngine.cpp.o: ../onnx/onnxruntime-linux-x64-1.15.1/include/onnxruntime_c_api.h
CMakeFiles/VideoSRLiteGUI.dir/src/SuperEigen/src/SuperResEngine.cpp.o: ../onnx/onnxruntime-linux-x64-1.15.1/include/onnxruntime_cxx_api.h
CMakeFiles/VideoSRLiteGUI.dir/src/SuperEigen/src/SuperResEngine.cpp.o: ../onnx/onnxruntime-linux-x64-1.15.1/include/onnxruntime_cxx_inline.h
CMakeFiles/VideoSRLiteGUI.dir/src/SuperEigen/src/SuperResEngine.cpp.o: ../src/DataStruct/FrameData.h
CMakeFiles/VideoSRLiteGUI.dir/src/SuperEigen/src/SuperResEngine.cpp.o: ../src/SuperEigen/include/ModelSession.h
CMakeFiles/VideoSRLiteGUI.dir/src/SuperEigen/src/SuperResEngine.cpp.o: ../src/SuperEigen/include/PrePostProcessor.h
CMakeFiles/VideoSRLiteGUI.dir/src/SuperEigen/src/SuperResEngine.cpp.o: ../src/SuperEigen/include/SuperResConfig.h
CMakeFiles/VideoSRLiteGUI.dir/src/SuperEigen/src/SuperResEngine.cpp.o: ../src/SuperEigen/include/SuperResEngine.h
CMakeFiles/VideoSRLiteGUI.dir/src/SuperEigen/src/SuperResEngine.cpp.o: ../src/SuperEigen/src/SuperResEngine.cpp
CMakeFiles/VideoSRLiteGUI.dir/src/SuperEigen/src/SuperResEngine.cpp.o: ../src/Utils/Logger.h

CMakeFiles/VideoSRLiteGUI.dir/src/SyncVA/AVSyncManager.cpp.o: ../src/DataStruct/AudioFrameData.h
CMakeFiles/VideoSRLiteGUI.dir/src/SyncVA/AVSyncManager.cpp.o: ../src/DataStruct/FrameData.h
CMakeFiles/VideoSRLiteGUI.dir/src/SyncVA/AVSyncManager.cpp.o: ../src/SyncVA/AVSyncManager.cpp
CMakeFiles/VideoSRLiteGUI.dir/src/SyncVA/AVSyncManager.cpp.o: ../src/SyncVA/AVSyncManager.h
CMakeFiles/VideoSRLiteGUI.dir/src/SyncVA/AVSyncManager.cpp.o: ../src/Utils/Logger.h

CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: ../src/UI/FileListWidget.cpp
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: ../src/UI/FileListWidget.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/QDateTime
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/QDebug
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/QFileInfo
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/QMimeData
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/QMimeDatabase
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/QStandardPaths
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/QUrl
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qabstractitemmodel.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qalgorithms.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qarraydata.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qatomic.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qatomic_bootstrap.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qatomic_cxx11.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qatomic_msvc.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qbasicatomic.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qbytearray.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qbytearraylist.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qchar.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qcompilerdetection.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qconfig-bootstrapped.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qconfig.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qcontainerfwd.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qcontiguouscache.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qcoreapplication.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qcoreevent.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qdatastream.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qdatetime.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qdebug.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qdir.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qeventloop.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qfile.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qfiledevice.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qfileinfo.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qflags.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qgenericatomic.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qglobal.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qglobalstatic.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qhash.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qhashfunctions.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qiodevice.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qitemselectionmodel.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qiterator.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qline.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qlist.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qlocale.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qlogging.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qmap.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qmargins.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qmetatype.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qmimedata.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qmimedatabase.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qmimetype.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qmutex.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qnamespace.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qnumeric.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qobject.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qobject_impl.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qobjectdefs.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qobjectdefs_impl.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qpair.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qpoint.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qprocessordetection.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qrect.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qrefcount.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qregexp.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qregularexpression.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qscopedpointer.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qset.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qshareddata.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qsharedpointer.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qsharedpointer_impl.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qsize.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qstandardpaths.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qstring.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qstringbuilder.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qstringlist.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qstringmatcher.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qsysinfo.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qsystemdetection.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qtcore-config.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qtextstream.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qtypeinfo.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qurl.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qurlquery.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qvariant.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qvarlengtharray.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qvector.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qversiontagging.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/QDragEnterEvent
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/QDropEvent
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qbrush.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qcolor.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qcursor.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qevent.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qfont.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qfontinfo.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qfontmetrics.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qguiapplication.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qicon.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qimage.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qinputmethod.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qkeysequence.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qmatrix.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qpaintdevice.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qpainterpath.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qpalette.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qpixelformat.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qpixmap.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qpolygon.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qregion.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qrgb.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qrgba64.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qtgui-config.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qtguiglobal.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qtouchdevice.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qtransform.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qvalidator.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qvector2d.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qwindowdefs.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qwindowdefs_win.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QApplication
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QFileDialog
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QGroupBox
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QHBoxLayout
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QLabel
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QListWidget
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QListWidgetItem
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QMessageBox
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QProgressBar
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QPushButton
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QVBoxLayout
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QWidget
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qabstractbutton.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qabstractitemdelegate.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qabstractitemview.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qabstractscrollarea.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qabstractslider.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qabstractspinbox.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qapplication.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qboxlayout.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qdesktopwidget.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qdialog.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qfiledialog.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qframe.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qgridlayout.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qgroupbox.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qlabel.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qlayout.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qlayoutitem.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qlistview.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qlistwidget.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qmessagebox.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qprogressbar.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qpushbutton.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qrubberband.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qsizepolicy.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qslider.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qstyle.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qstyleoption.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qtabbar.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qtabwidget.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qtwidgets-config.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qtwidgetsglobal.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qwidget.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o: VideoSRLiteGUI_autogen/include/FileListWidget.moc

CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: ../src/UI/ImagePreviewWidget.cpp
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: ../src/UI/ImagePreviewWidget.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/QDateTime
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/QDebug
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/QSize
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/QStandardPaths
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/QTimer
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qabstractitemmodel.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qalgorithms.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qarraydata.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qatomic.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qatomic_bootstrap.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qatomic_cxx11.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qatomic_msvc.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qbasicatomic.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qbasictimer.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qbytearray.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qbytearraylist.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qchar.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qcompilerdetection.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qconfig-bootstrapped.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qconfig.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qcontainerfwd.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qcontiguouscache.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qcoreevent.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qdatastream.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qdatetime.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qdebug.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qdir.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qfile.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qfiledevice.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qfileinfo.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qflags.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qgenericatomic.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qglobal.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qglobalstatic.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qhash.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qhashfunctions.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qiodevice.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qiterator.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qline.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qlist.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qlocale.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qlogging.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qmap.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qmargins.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qmetatype.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qmutex.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qnamespace.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qnumeric.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qobject.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qobject_impl.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qobjectdefs.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qobjectdefs_impl.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qpair.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qpoint.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qprocessordetection.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qrect.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qrefcount.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qregexp.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qregularexpression.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qscopedpointer.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qset.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qshareddata.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qsharedpointer.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qsharedpointer_impl.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qsize.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qstandardpaths.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qstring.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qstringbuilder.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qstringlist.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qstringmatcher.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qsysinfo.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qsystemdetection.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qtcore-config.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qtextstream.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qtimer.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qtypeinfo.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qurl.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qurlquery.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qvariant.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qvarlengtharray.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qvector.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qversiontagging.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/QPixmap
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qbrush.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qcolor.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qcursor.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qevent.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qfont.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qfontinfo.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qfontmetrics.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qicon.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qimage.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qkeysequence.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qmatrix.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qpaintdevice.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qpainterpath.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qpalette.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qpixelformat.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qpixmap.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qpolygon.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qregion.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qrgb.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qrgba64.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qtgui-config.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qtguiglobal.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qtouchdevice.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qtransform.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qvalidator.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qvector2d.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qwindowdefs.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qwindowdefs_win.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QComboBox
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QFileDialog
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QGroupBox
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QHBoxLayout
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QLabel
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QMessageBox
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QPushButton
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QScrollArea
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QScrollBar
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QStackedWidget
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QVBoxLayout
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QWidget
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qabstractbutton.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qabstractitemdelegate.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qabstractscrollarea.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qabstractslider.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qabstractspinbox.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qboxlayout.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qcombobox.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qdialog.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qfiledialog.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qframe.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qgridlayout.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qgroupbox.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qlabel.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qlayout.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qlayoutitem.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qmessagebox.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qpushbutton.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qrubberband.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qscrollarea.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qscrollbar.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qsizepolicy.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qslider.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qstackedwidget.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qstyle.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qstyleoption.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qtabbar.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qtabwidget.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qtwidgets-config.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qtwidgetsglobal.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qwidget.h

CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: ../onnx/onnxruntime-linux-x64-1.15.1/include/onnxruntime_c_api.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: ../onnx/onnxruntime-linux-x64-1.15.1/include/onnxruntime_cxx_api.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: ../onnx/onnxruntime-linux-x64-1.15.1/include/onnxruntime_cxx_inline.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: ../src/DataStruct/FrameData.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: ../src/SuperEigen/include/ModelSession.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: ../src/SuperEigen/include/PrePostProcessor.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: ../src/SuperEigen/include/SuperResConfig.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: ../src/SuperEigen/include/SuperResEngine.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: ../src/UI/ImageProcessor.cpp
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: ../src/UI/ImageProcessor.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/QAtomicInt
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/QDebug
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/QElapsedTimer
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/QMutex
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/QObject
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/QQueue
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/QRunnable
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/QThread
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/QThreadPool
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/QWaitCondition
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qalgorithms.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qarraydata.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qatomic.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qatomic_bootstrap.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qatomic_cxx11.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qatomic_msvc.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qbasicatomic.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qbytearray.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qbytearraylist.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qchar.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qcompilerdetection.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qconfig-bootstrapped.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qconfig.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qcontainerfwd.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qcontiguouscache.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qcoreapplication.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qcoreevent.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qdatastream.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qdebug.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qelapsedtimer.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qeventloop.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qfile.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qfiledevice.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qflags.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qgenericatomic.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qglobal.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qglobalstatic.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qhash.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qhashfunctions.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qiodevice.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qiterator.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qline.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qlist.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qlocale.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qlogging.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qmap.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qmargins.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qmetatype.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qmutex.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qnamespace.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qnumeric.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qobject.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qobject_impl.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qobjectdefs.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qobjectdefs_impl.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qpair.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qpoint.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qprocessordetection.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qqueue.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qrect.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qrefcount.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qregexp.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qrunnable.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qscopedpointer.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qset.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qshareddata.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qsharedpointer.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qsharedpointer_impl.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qsize.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qstring.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qstringbuilder.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qstringlist.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qstringmatcher.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qsysinfo.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qsystemdetection.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qtcore-config.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qtextstream.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qthread.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qthreadpool.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qtypeinfo.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qurl.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qurlquery.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qvariant.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qvarlengtharray.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qvector.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qversiontagging.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qwaitcondition.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qbrush.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qcolor.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qcursor.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qevent.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qfont.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qfontinfo.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qfontmetrics.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qguiapplication.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qimage.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qinputmethod.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qkeysequence.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qmatrix.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qpaintdevice.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qpainterpath.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qpalette.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qpixelformat.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qpixmap.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qpolygon.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qregion.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qrgb.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qrgba64.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qtgui-config.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qtguiglobal.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qtouchdevice.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qtransform.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qvector2d.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qwindowdefs.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qwindowdefs_win.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QApplication
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qapplication.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qdesktopwidget.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qsizepolicy.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qtwidgets-config.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qtwidgetsglobal.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qwidget.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o: VideoSRLiteGUI_autogen/include/ImageProcessor.moc

CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: ../onnx/onnxruntime-linux-x64-1.15.1/include/onnxruntime_c_api.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: ../onnx/onnxruntime-linux-x64-1.15.1/include/onnxruntime_cxx_api.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: ../onnx/onnxruntime-linux-x64-1.15.1/include/onnxruntime_cxx_inline.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: ../src/DataStruct/FrameData.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: ../src/SuperEigen/include/ModelSession.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: ../src/SuperEigen/include/PrePostProcessor.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: ../src/SuperEigen/include/SuperResConfig.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: ../src/SuperEigen/include/SuperResEngine.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: ../src/UI/FileListWidget.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: ../src/UI/ImagePreviewWidget.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: ../src/UI/ImageProcessor.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: ../src/UI/MainWindow.cpp
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: ../src/UI/MainWindow.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: ../src/UI/SettingsPanel.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: ../src/WorkerPool/WorkerPool.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/QAtomicInt
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/QDateTime
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/QDebug
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/QDir
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/QElapsedTimer
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/QFileInfo
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/QMap
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/QMimeData
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/QMimeDatabase
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/QMutex
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/QObject
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/QQueue
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/QRunnable
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/QSize
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/QStandardPaths
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/QStringList
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/QThread
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/QThreadPool
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/QTimer
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/QUrl
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/QUuid
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/QVariant
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/QWaitCondition
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qabstractitemmodel.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qalgorithms.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qarraydata.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qatomic.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qatomic_bootstrap.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qatomic_cxx11.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qatomic_msvc.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qbasicatomic.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qbasictimer.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qbytearray.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qbytearraylist.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qchar.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qcompilerdetection.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qconfig-bootstrapped.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qconfig.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qcontainerfwd.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qcontiguouscache.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qcoreapplication.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qcoreevent.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qdatastream.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qdatetime.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qdebug.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qdir.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qelapsedtimer.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qeventloop.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qfile.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qfiledevice.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qfileinfo.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qflags.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qgenericatomic.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qglobal.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qglobalstatic.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qhash.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qhashfunctions.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qiodevice.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qitemselectionmodel.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qiterator.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qline.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qlist.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qlocale.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qlogging.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qmap.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qmargins.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qmetatype.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qmimedata.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qmimedatabase.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qmimetype.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qmutex.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qnamespace.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qnumeric.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qobject.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qobject_impl.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qobjectdefs.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qobjectdefs_impl.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qpair.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qpoint.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qprocessordetection.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qqueue.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qrect.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qrefcount.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qregexp.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qregularexpression.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qrunnable.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qscopedpointer.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qset.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qshareddata.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qsharedpointer.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qsharedpointer_impl.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qsize.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qstandardpaths.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qstring.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qstringbuilder.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qstringlist.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qstringmatcher.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qsysinfo.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qsystemdetection.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qtcore-config.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qtextstream.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qthread.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qthreadpool.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qtimer.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qtypeinfo.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qurl.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qurlquery.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/quuid.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qvariant.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qvarlengtharray.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qvector.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qversiontagging.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qwaitcondition.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/QCloseEvent
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/QDragEnterEvent
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/QDropEvent
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/QKeyEvent
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/QPainter
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/QPixmap
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qbrush.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qcolor.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qcursor.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qevent.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qfont.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qfontinfo.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qfontmetrics.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qguiapplication.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qicon.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qimage.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qinputmethod.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qkeysequence.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qmatrix.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qpaintdevice.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qpainter.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qpainterpath.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qpalette.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qpen.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qpixelformat.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qpixmap.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qpolygon.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qregion.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qrgb.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qrgba64.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qtextcursor.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qtextdocument.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qtextformat.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qtextoption.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qtgui-config.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qtguiglobal.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qtouchdevice.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qtransform.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qvalidator.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qvector2d.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qwindowdefs.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qwindowdefs_win.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QAction
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QApplication
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QButtonGroup
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QCheckBox
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QComboBox
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QDoubleSpinBox
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QFileDialog
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QGridLayout
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QGroupBox
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QHBoxLayout
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QHeaderView
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QLabel
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QLineEdit
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QListWidget
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QListWidgetItem
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QMainWindow
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QMenu
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QMenuBar
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QMessageBox
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QProgressBar
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QPushButton
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QRadioButton
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QScrollArea
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QScrollBar
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QSlider
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QSpacerItem
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QSpinBox
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QSplitter
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QStackedWidget
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QStatusBar
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QTextEdit
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QVBoxLayout
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QWidget
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qabstractbutton.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qabstractitemdelegate.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qabstractitemview.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qabstractscrollarea.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qabstractslider.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qabstractspinbox.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qaction.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qactiongroup.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qapplication.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qboxlayout.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qbuttongroup.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qcheckbox.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qcombobox.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qdesktopwidget.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qdialog.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qfiledialog.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qframe.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qgridlayout.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qgroupbox.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qheaderview.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qlabel.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qlayout.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qlayoutitem.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qlineedit.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qlistview.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qlistwidget.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qmainwindow.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qmenu.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qmenubar.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qmessagebox.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qprogressbar.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qpushbutton.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qradiobutton.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qrubberband.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qscrollarea.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qscrollbar.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qsizepolicy.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qslider.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qspinbox.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qsplitter.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qstackedwidget.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qstatusbar.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qstyle.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qstyleoption.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qtabbar.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qtabwidget.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qtextedit.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qtwidgets-config.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qtwidgetsglobal.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qwidget.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o: VideoSRLiteGUI_autogen/include/ui_MainWindow.h

CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: ../src/UI/SettingsPanel.cpp
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: ../src/UI/SettingsPanel.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/QDebug
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/QDir
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/QSettings
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/QStandardPaths
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qabstractitemmodel.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qalgorithms.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qarraydata.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qatomic.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qatomic_bootstrap.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qatomic_cxx11.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qatomic_msvc.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qbasicatomic.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qbytearray.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qbytearraylist.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qchar.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qcompilerdetection.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qconfig-bootstrapped.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qconfig.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qcontainerfwd.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qcontiguouscache.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qcoreevent.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qdatastream.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qdebug.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qdir.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qfile.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qfiledevice.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qfileinfo.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qflags.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qgenericatomic.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qglobal.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qglobalstatic.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qhash.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qhashfunctions.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qiodevice.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qiterator.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qline.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qlist.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qlocale.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qlogging.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qmap.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qmargins.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qmetatype.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qmutex.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qnamespace.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qnumeric.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qobject.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qobject_impl.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qobjectdefs.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qobjectdefs_impl.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qpair.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qpoint.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qprocessordetection.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qrect.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qrefcount.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qregexp.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qregularexpression.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qscopedpointer.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qset.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qsettings.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qshareddata.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qsharedpointer.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qsharedpointer_impl.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qsize.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qstandardpaths.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qstring.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qstringbuilder.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qstringlist.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qstringmatcher.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qsysinfo.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qsystemdetection.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qtcore-config.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qtextstream.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qtypeinfo.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qurl.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qurlquery.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qvariant.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qvarlengtharray.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qvector.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qversiontagging.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qbrush.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qcolor.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qcursor.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qevent.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qfont.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qfontinfo.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qfontmetrics.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qicon.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qimage.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qkeysequence.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qmatrix.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qpaintdevice.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qpainterpath.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qpalette.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qpen.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qpixelformat.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qpixmap.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qpolygon.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qregion.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qrgb.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qrgba64.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qtextcursor.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qtextformat.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qtextoption.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qtgui-config.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qtguiglobal.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qtouchdevice.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qtransform.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qvalidator.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qvector2d.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qwindowdefs.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qwindowdefs_win.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QCheckBox
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QComboBox
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QDoubleSpinBox
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QFileDialog
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QGridLayout
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QGroupBox
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QHBoxLayout
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QInputDialog
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QLabel
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QLineEdit
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QMessageBox
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QPushButton
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QSlider
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QSpinBox
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QVBoxLayout
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QWidget
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qabstractbutton.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qabstractitemdelegate.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qabstractslider.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qabstractspinbox.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qboxlayout.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qcheckbox.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qcombobox.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qdialog.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qfiledialog.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qframe.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qgridlayout.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qgroupbox.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qinputdialog.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qlabel.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qlayout.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qlayoutitem.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qlineedit.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qmessagebox.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qpushbutton.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qrubberband.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qsizepolicy.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qslider.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qspinbox.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qstyle.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qstyleoption.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qtabbar.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qtabwidget.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qtwidgets-config.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qtwidgetsglobal.h
CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qwidget.h

CMakeFiles/VideoSRLiteGUI.dir/src/Utils/FileUtils.cpp.o: ../src/Utils/FileUtils.cpp
CMakeFiles/VideoSRLiteGUI.dir/src/Utils/FileUtils.cpp.o: ../src/Utils/FileUtils.h

CMakeFiles/VideoSRLiteGUI.dir/src/Utils/LogUtils.cpp.o: ../src/Utils/LogUtils.cpp
CMakeFiles/VideoSRLiteGUI.dir/src/Utils/LogUtils.cpp.o: ../src/Utils/LogUtils.h

CMakeFiles/VideoSRLiteGUI.dir/src/Utils/Logger.cpp.o: ../src/Utils/Logger.cpp
CMakeFiles/VideoSRLiteGUI.dir/src/Utils/Logger.cpp.o: ../src/Utils/Logger.h

CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: ../src/WorkerPool/WorkerPool.cpp
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: ../src/WorkerPool/WorkerPool.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/QDebug
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/QMutex
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/QObject
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/QQueue
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/QThread
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/QWaitCondition
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qalgorithms.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qarraydata.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qatomic.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qatomic_bootstrap.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qatomic_cxx11.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qatomic_msvc.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qbasicatomic.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qbytearray.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qbytearraylist.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qchar.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qcompilerdetection.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qconfig-bootstrapped.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qconfig.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qcontainerfwd.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qcontiguouscache.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qcoreevent.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qdebug.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qflags.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qgenericatomic.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qglobal.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qglobalstatic.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qhash.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qhashfunctions.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qiodevice.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qiterator.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qlist.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qlocale.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qlogging.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qmap.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qmetatype.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qmutex.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qnamespace.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qnumeric.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qobject.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qobject_impl.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qobjectdefs.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qobjectdefs_impl.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qpair.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qpoint.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qprocessordetection.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qqueue.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qrefcount.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qregexp.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qscopedpointer.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qset.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qshareddata.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qsharedpointer.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qsharedpointer_impl.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qstring.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qstringbuilder.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qstringlist.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qstringmatcher.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qsysinfo.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qsystemdetection.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qtcore-config.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qtextstream.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qthread.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qtypeinfo.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qvariant.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qvarlengtharray.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qvector.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qversiontagging.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qwaitcondition.h
CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o: VideoSRLiteGUI_autogen/include/WorkerPool.moc

CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: ../onnx/onnxruntime-linux-x64-1.15.1/include/onnxruntime_c_api.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: ../onnx/onnxruntime-linux-x64-1.15.1/include/onnxruntime_cxx_api.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: ../onnx/onnxruntime-linux-x64-1.15.1/include/onnxruntime_cxx_inline.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: ../src/DataStruct/FrameData.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: ../src/SuperEigen/include/ModelSession.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: ../src/SuperEigen/include/PrePostProcessor.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: ../src/SuperEigen/include/SuperResConfig.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: ../src/SuperEigen/include/SuperResEngine.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: ../src/UI/FileListWidget.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: ../src/UI/ImagePreviewWidget.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: ../src/UI/ImageProcessor.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: ../src/UI/MainWindow.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: ../src/UI/SettingsPanel.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: ../src/WorkerPool/WorkerPool.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: ../src/main.cpp
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/QAtomicInt
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/QDateTime
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/QElapsedTimer
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/QFileInfo
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/QMap
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/QMetaType
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/QMimeData
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/QMutex
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/QObject
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/QQueue
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/QRunnable
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/QSize
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/QStandardPaths
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/QStringList
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/QThread
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/QThreadPool
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/QTimer
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/QUrl
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/QWaitCondition
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qabstractitemmodel.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qalgorithms.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qarraydata.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qatomic.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qatomic_bootstrap.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qatomic_cxx11.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qatomic_msvc.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qbasicatomic.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qbasictimer.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qbytearray.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qbytearraylist.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qchar.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qcompilerdetection.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qconfig-bootstrapped.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qconfig.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qcontainerfwd.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qcontiguouscache.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qcoreapplication.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qcoreevent.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qdatastream.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qdatetime.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qdebug.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qdir.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qelapsedtimer.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qeventloop.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qfile.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qfiledevice.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qfileinfo.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qflags.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qgenericatomic.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qglobal.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qglobalstatic.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qhash.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qhashfunctions.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qiodevice.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qitemselectionmodel.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qiterator.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qline.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qlist.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qlocale.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qlogging.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qmap.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qmargins.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qmetatype.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qmimedata.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qmutex.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qnamespace.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qnumeric.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qobject.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qobject_impl.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qobjectdefs.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qobjectdefs_impl.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qpair.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qpoint.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qprocessordetection.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qqueue.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qrect.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qrefcount.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qregexp.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qregularexpression.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qrunnable.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qscopedpointer.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qset.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qshareddata.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qsharedpointer.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qsharedpointer_impl.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qsize.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qstandardpaths.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qstring.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qstringbuilder.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qstringlist.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qstringmatcher.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qsysinfo.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qsystemdetection.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qtcore-config.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qtextstream.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qthread.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qthreadpool.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qtimer.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qtypeinfo.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qurl.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qurlquery.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qvariant.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qvarlengtharray.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qvector.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qversiontagging.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtCore/qwaitcondition.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/QCloseEvent
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/QDragEnterEvent
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/QDropEvent
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/QPixmap
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qbrush.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qcolor.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qcursor.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qevent.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qfont.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qfontinfo.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qfontmetrics.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qguiapplication.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qicon.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qimage.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qinputmethod.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qkeysequence.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qmatrix.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qpaintdevice.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qpainterpath.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qpalette.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qpen.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qpixelformat.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qpixmap.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qpolygon.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qregion.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qrgb.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qrgba64.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qtextcursor.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qtextdocument.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qtextformat.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qtextoption.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qtgui-config.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qtguiglobal.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qtouchdevice.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qtransform.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qvalidator.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qvector2d.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qwindowdefs.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtGui/qwindowdefs_win.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QApplication
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QCheckBox
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QComboBox
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QDoubleSpinBox
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QFileDialog
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QGridLayout
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QGroupBox
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QHBoxLayout
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QLabel
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QLineEdit
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QListWidget
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QListWidgetItem
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QMainWindow
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QMessageBox
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QProgressBar
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QPushButton
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QScrollArea
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QScrollBar
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QSlider
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QSpinBox
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QSplitter
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QStackedWidget
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QTextEdit
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QVBoxLayout
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QWidget
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qabstractbutton.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qabstractitemdelegate.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qabstractitemview.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qabstractscrollarea.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qabstractslider.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qabstractspinbox.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qapplication.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qboxlayout.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qcheckbox.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qcombobox.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qdesktopwidget.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qdialog.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qfiledialog.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qframe.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qgridlayout.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qgroupbox.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qlabel.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qlayout.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qlayoutitem.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qlineedit.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qlistview.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qlistwidget.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qmainwindow.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qmessagebox.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qprogressbar.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qpushbutton.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qrubberband.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qscrollarea.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qscrollbar.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qsizepolicy.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qslider.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qspinbox.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qsplitter.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qstackedwidget.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qstyle.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qstyleoption.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qtabbar.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qtabwidget.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qtextedit.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qtwidgets-config.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qtwidgetsglobal.h
CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o: /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qwidget.h

