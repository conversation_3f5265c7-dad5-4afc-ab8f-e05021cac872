# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  "CXX"
  )
# The set of files for implicit dependencies of each language:
set(CMAKE_DEPENDS_CHECK_CXX
  "/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build/VideoSRLiteGUI_autogen/mocs_compilation.cpp" "/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build/CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o"
  "/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/AppController/AppController.cpp" "/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build/CMakeFiles/VideoSRLiteGUI.dir/src/AppController/AppController.cpp.o"
  "/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/AppController/WorkerPool.cpp" "/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build/CMakeFiles/VideoSRLiteGUI.dir/src/AppController/WorkerPool.cpp.o"
  "/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/AudioProc/AudioProc.cpp" "/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build/CMakeFiles/VideoSRLiteGUI.dir/src/AudioProc/AudioProc.cpp.o"
  "/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/AudioProcessor/AudioProcessor.cpp" "/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build/CMakeFiles/VideoSRLiteGUI.dir/src/AudioProcessor/AudioProcessor.cpp.o"
  "/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Decoder/src/AudioDecoder.cpp" "/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build/CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/AudioDecoder.cpp.o"
  "/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Decoder/src/Decoder.cpp" "/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build/CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/Decoder.cpp.o"
  "/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Decoder/src/VideoDecoder.cpp" "/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build/CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/VideoDecoder.cpp.o"
  "/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Encoder/AudioEncoder.cpp" "/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build/CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/AudioEncoder.cpp.o"
  "/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Encoder/Encoder.cpp" "/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build/CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o"
  "/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Encoder/Muxer.cpp" "/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build/CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Muxer.cpp.o"
  "/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Encoder/VideoEncoder.cpp" "/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build/CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/VideoEncoder.cpp.o"
  "/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/PostFilter/PostFilter.cpp" "/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build/CMakeFiles/VideoSRLiteGUI.dir/src/PostFilter/PostFilter.cpp.o"
  "/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/PostFilter/PostFilterProcessor.cpp" "/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build/CMakeFiles/VideoSRLiteGUI.dir/src/PostFilter/PostFilterProcessor.cpp.o"
  "/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Processing/AudioDenoiser.cpp" "/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build/CMakeFiles/VideoSRLiteGUI.dir/src/Processing/AudioDenoiser.cpp.o"
  "/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Processing/PostProcessor.cpp" "/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build/CMakeFiles/VideoSRLiteGUI.dir/src/Processing/PostProcessor.cpp.o"
  "/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Processing/SuperResolution.cpp" "/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build/CMakeFiles/VideoSRLiteGUI.dir/src/Processing/SuperResolution.cpp.o"
  "/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/SuperEigen/src/ModelSession.cpp" "/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build/CMakeFiles/VideoSRLiteGUI.dir/src/SuperEigen/src/ModelSession.cpp.o"
  "/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/SuperEigen/src/PrePostProcessor.cpp" "/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build/CMakeFiles/VideoSRLiteGUI.dir/src/SuperEigen/src/PrePostProcessor.cpp.o"
  "/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/SuperEigen/src/SuperResConfig.cpp" "/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build/CMakeFiles/VideoSRLiteGUI.dir/src/SuperEigen/src/SuperResConfig.cpp.o"
  "/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/SuperEigen/src/SuperResEngine.cpp" "/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build/CMakeFiles/VideoSRLiteGUI.dir/src/SuperEigen/src/SuperResEngine.cpp.o"
  "/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/SyncVA/AVSyncManager.cpp" "/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build/CMakeFiles/VideoSRLiteGUI.dir/src/SyncVA/AVSyncManager.cpp.o"
  "/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/UI/FileListWidget.cpp" "/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build/CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o"
  "/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/UI/ImagePreviewWidget.cpp" "/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build/CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o"
  "/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/UI/ImageProcessor.cpp" "/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build/CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o"
  "/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/UI/MainWindow.cpp" "/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build/CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o"
  "/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/UI/SettingsPanel.cpp" "/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build/CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o"
  "/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Utils/FileUtils.cpp" "/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build/CMakeFiles/VideoSRLiteGUI.dir/src/Utils/FileUtils.cpp.o"
  "/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Utils/LogUtils.cpp" "/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build/CMakeFiles/VideoSRLiteGUI.dir/src/Utils/LogUtils.cpp.o"
  "/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Utils/Logger.cpp" "/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build/CMakeFiles/VideoSRLiteGUI.dir/src/Utils/Logger.cpp.o"
  "/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/WorkerPool/WorkerPool.cpp" "/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build/CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o"
  "/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/main.cpp" "/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build/CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o"
  )
set(CMAKE_CXX_COMPILER_ID "GNU")

# Preprocessor definitions for this target.
set(CMAKE_TARGET_DEFINITIONS_CXX
  "HAVE_ONNXRUNTIME"
  "QT_CORE_LIB"
  "QT_GUI_LIB"
  "QT_NO_DEBUG"
  "QT_WIDGETS_LIB"
  )

# The include file search paths:
set(CMAKE_CXX_TARGET_INCLUDE_PATH
  "VideoSRLiteGUI_autogen/include"
  "../src"
  "../src/Processing"
  "../src/UI"
  "../src/Utils"
  "../src/DataStruct"
  "../src/Decoder/include"
  "../src/SuperEigen/include"
  "../src/AppController"
  "../src/AudioProcessor"
  "../src/AudioProc"
  "../src/Encoder"
  "../src/SyncVA"
  "../src/PostFilter"
  "../src/WorkerPool"
  "../src/Core"
  "../onnx/onnxruntime-linux-x64-1.15.1/include"
  "/usr/include/x86_64-linux-gnu"
  "/usr/include/opencv"
  "/usr/include/x86_64-linux-gnu/qt5"
  "/usr/include/x86_64-linux-gnu/qt5/QtCore"
  "/usr/lib/x86_64-linux-gnu/qt5/mkspecs/linux-g++"
  "/usr/include/x86_64-linux-gnu/qt5/QtWidgets"
  "/usr/include/x86_64-linux-gnu/qt5/QtGui"
  )

# Targets to which this target links.
set(CMAKE_TARGET_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
