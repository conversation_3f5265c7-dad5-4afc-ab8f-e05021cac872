# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.10

# compile CXX with g++-8
CXX_FLAGS = -O3 -DNDEBUG   -fPIC -std=gnu++1z

CXX_DEFINES = -DHAVE_ONNXRUNTIME -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NO_DEBUG -DQT_WIDGETS_LIB

CXX_INCLUDES = -I/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build/VideoSRLiteGUI_autogen/include -I/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src -I/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Processing -I/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/UI -I/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Utils -I/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/DataStruct -I/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Decoder/include -I/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/SuperEigen/include -I/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/AppController -I/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/AudioProcessor -I/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/AudioProc -I/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Encoder -I/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/SyncVA -I/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/PostFilter -I/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/WorkerPool -I/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Core -I/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/onnx/onnxruntime-linux-x64-1.15.1/include -isystem /usr/include/x86_64-linux-gnu -isystem /usr/include/opencv -isystem /usr/include/x86_64-linux-gnu/qt5 -isystem /usr/include/x86_64-linux-gnu/qt5/QtCore -isystem /usr/lib/x86_64-linux-gnu/qt5/mkspecs/linux-g++ -isystem /usr/include/x86_64-linux-gnu/qt5/QtWidgets -isystem /usr/include/x86_64-linux-gnu/qt5/QtGui 

