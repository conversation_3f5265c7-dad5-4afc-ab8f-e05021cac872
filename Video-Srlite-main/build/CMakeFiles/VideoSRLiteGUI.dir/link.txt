/usr/bin/c++  -O3 -DNDEBUG   CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o CMakeFiles/VideoSRLiteGUI.dir/src/Utils/FileUtils.cpp.o CMakeFiles/VideoSRLiteGUI.dir/src/Utils/LogUtils.cpp.o CMakeFiles/VideoSRLiteGUI.dir/src/Utils/Logger.cpp.o CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/Decoder.cpp.o CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/VideoDecoder.cpp.o CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/AudioDecoder.cpp.o CMakeFiles/VideoSRLiteGUI.dir/src/Processing/AudioDenoiser.cpp.o CMakeFiles/VideoSRLiteGUI.dir/src/Processing/PostProcessor.cpp.o CMakeFiles/VideoSRLiteGUI.dir/src/Processing/SuperResolution.cpp.o CMakeFiles/VideoSRLiteGUI.dir/src/AppController/AppController.cpp.o CMakeFiles/VideoSRLiteGUI.dir/src/AppController/WorkerPool.cpp.o CMakeFiles/VideoSRLiteGUI.dir/src/AudioProcessor/AudioProcessor.cpp.o CMakeFiles/VideoSRLiteGUI.dir/src/AudioProc/AudioProc.cpp.o CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/VideoEncoder.cpp.o CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/AudioEncoder.cpp.o CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Muxer.cpp.o CMakeFiles/VideoSRLiteGUI.dir/src/SyncVA/AVSyncManager.cpp.o CMakeFiles/VideoSRLiteGUI.dir/src/PostFilter/PostFilterProcessor.cpp.o CMakeFiles/VideoSRLiteGUI.dir/src/PostFilter/PostFilter.cpp.o CMakeFiles/VideoSRLiteGUI.dir/src/SuperEigen/src/SuperResEngine.cpp.o CMakeFiles/VideoSRLiteGUI.dir/src/SuperEigen/src/ModelSession.cpp.o CMakeFiles/VideoSRLiteGUI.dir/src/SuperEigen/src/PrePostProcessor.cpp.o CMakeFiles/VideoSRLiteGUI.dir/src/SuperEigen/src/SuperResConfig.cpp.o CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o  -o bin/VideoSRLiteGUI  -Wl,-rpath,/home/<USER>/Public/video_lite/VideoSR-Lite/onnx/onnxruntime-linux-x64-1.15.1/lib: /usr/lib/x86_64-linux-gnu/libopencv_stitching.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_aruco.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_bgsegm.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_bioinspired.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_ccalib.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_dnn_objdetect.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_dnn_superres.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_dpm.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_face.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_freetype.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_fuzzy.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_hdf.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_hfs.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_img_hash.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_line_descriptor.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_quality.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_reg.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_rgbd.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_saliency.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_shape.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_stereo.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_structured_light.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_superres.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_surface_matching.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_tracking.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_videostab.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_viz.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_xobjdetect.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_xphoto.so.4.2.0 -lpthread /usr/lib/x86_64-linux-gnu/libQt5Widgets.so.5.12.8 /usr/lib/x86_64-linux-gnu/libQt5Gui.so.5.12.8 ../onnx/onnxruntime-linux-x64-1.15.1/lib/libonnxruntime.so /usr/lib/x86_64-linux-gnu/libavcodec.so /usr/lib/x86_64-linux-gnu/libavformat.so /usr/lib/x86_64-linux-gnu/libavutil.so /usr/lib/x86_64-linux-gnu/libswscale.so /usr/lib/x86_64-linux-gnu/libswresample.so /usr/lib/x86_64-linux-gnu/libopencv_highgui.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_datasets.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_plot.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_text.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_dnn.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_ml.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_phase_unwrapping.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_optflow.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_ximgproc.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_video.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_videoio.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_imgcodecs.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_objdetect.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_calib3d.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_features2d.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_flann.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_photo.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_imgproc.so.4.2.0 /usr/lib/x86_64-linux-gnu/libopencv_core.so.4.2.0 /usr/lib/x86_64-linux-gnu/libQt5Core.so.5.12.8 
