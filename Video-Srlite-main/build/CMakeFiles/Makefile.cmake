# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# The generator used is:
set(CMAKE_DEPENDS_GENERATOR "Unix Makefiles")

# The top level Makefile was generated from the following files:
set(CMAKE_MAKEFILE_DEPENDS
  "CMakeCache.txt"
  "../CMakeLists.txt"
  "CMakeFiles/3.16.3/CMakeCCompiler.cmake"
  "CMakeFiles/3.16.3/CMakeCXXCompiler.cmake"
  "CMakeFiles/3.16.3/CMakeSystem.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/Qt5/Qt5Config.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/Qt5/Qt5ConfigVersion.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/Qt5/Qt5ModuleLocation.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/Qt5Core/Qt5CoreConfig.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/Qt5Core/Qt5CoreConfigExtras.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/Qt5Core/Qt5CoreConfigExtrasMkspecDir.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/Qt5Core/Qt5CoreConfigVersion.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/Qt5Core/Qt5CoreMacros.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/Qt5Gui/Qt5GuiConfig.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/Qt5Gui/Qt5GuiConfigExtras.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/Qt5Gui/Qt5GuiConfigVersion.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/Qt5Gui/Qt5Gui_QComposePlatformInputContextPlugin.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/Qt5Gui/Qt5Gui_QEglFSEmulatorIntegrationPlugin.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/Qt5Gui/Qt5Gui_QEglFSIntegrationPlugin.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/Qt5Gui/Qt5Gui_QEglFSKmsEglDeviceIntegrationPlugin.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/Qt5Gui/Qt5Gui_QEglFSKmsGbmIntegrationPlugin.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/Qt5Gui/Qt5Gui_QEglFSX11IntegrationPlugin.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/Qt5Gui/Qt5Gui_QEvdevKeyboardPlugin.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/Qt5Gui/Qt5Gui_QEvdevMousePlugin.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/Qt5Gui/Qt5Gui_QEvdevTabletPlugin.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/Qt5Gui/Qt5Gui_QEvdevTouchScreenPlugin.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/Qt5Gui/Qt5Gui_QGifPlugin.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/Qt5Gui/Qt5Gui_QICOPlugin.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/Qt5Gui/Qt5Gui_QIbusPlatformInputContextPlugin.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/Qt5Gui/Qt5Gui_QJpegPlugin.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/Qt5Gui/Qt5Gui_QLibInputPlugin.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/Qt5Gui/Qt5Gui_QLinuxFbIntegrationPlugin.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/Qt5Gui/Qt5Gui_QMinimalEglIntegrationPlugin.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/Qt5Gui/Qt5Gui_QMinimalIntegrationPlugin.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/Qt5Gui/Qt5Gui_QOffscreenIntegrationPlugin.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/Qt5Gui/Qt5Gui_QTuioTouchPlugin.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/Qt5Gui/Qt5Gui_QVncIntegrationPlugin.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/Qt5Gui/Qt5Gui_QXcbEglIntegrationPlugin.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/Qt5Gui/Qt5Gui_QXcbGlxIntegrationPlugin.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/Qt5Gui/Qt5Gui_QXcbIntegrationPlugin.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/Qt5Widgets/Qt5WidgetsConfig.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/Qt5Widgets/Qt5WidgetsConfigExtras.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/Qt5Widgets/Qt5WidgetsConfigVersion.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/Qt5Widgets/Qt5WidgetsMacros.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/opencv4/OpenCVConfig-version.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/opencv4/OpenCVConfig.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/opencv4/OpenCVModules-release.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/opencv4/OpenCVModules.cmake"
  "/usr/share/cmake-3.16/Modules/CMakeCInformation.cmake"
  "/usr/share/cmake-3.16/Modules/CMakeCXXInformation.cmake"
  "/usr/share/cmake-3.16/Modules/CMakeCheckCompilerFlagCommonPatterns.cmake"
  "/usr/share/cmake-3.16/Modules/CMakeCommonLanguageInclude.cmake"
  "/usr/share/cmake-3.16/Modules/CMakeGenericSystem.cmake"
  "/usr/share/cmake-3.16/Modules/CMakeInitializeConfigs.cmake"
  "/usr/share/cmake-3.16/Modules/CMakeLanguageInformation.cmake"
  "/usr/share/cmake-3.16/Modules/CMakeParseArguments.cmake"
  "/usr/share/cmake-3.16/Modules/CMakeSystemSpecificInformation.cmake"
  "/usr/share/cmake-3.16/Modules/CMakeSystemSpecificInitialize.cmake"
  "/usr/share/cmake-3.16/Modules/CheckCSourceCompiles.cmake"
  "/usr/share/cmake-3.16/Modules/CheckIncludeFile.cmake"
  "/usr/share/cmake-3.16/Modules/CheckLibraryExists.cmake"
  "/usr/share/cmake-3.16/Modules/Compiler/CMakeCommonCompilerMacros.cmake"
  "/usr/share/cmake-3.16/Modules/Compiler/GNU-C.cmake"
  "/usr/share/cmake-3.16/Modules/Compiler/GNU-CXX.cmake"
  "/usr/share/cmake-3.16/Modules/Compiler/GNU.cmake"
  "/usr/share/cmake-3.16/Modules/FindPackageHandleStandardArgs.cmake"
  "/usr/share/cmake-3.16/Modules/FindPackageMessage.cmake"
  "/usr/share/cmake-3.16/Modules/FindPkgConfig.cmake"
  "/usr/share/cmake-3.16/Modules/FindThreads.cmake"
  "/usr/share/cmake-3.16/Modules/Internal/CMakeCheckCompilerFlag.cmake"
  "/usr/share/cmake-3.16/Modules/Platform/Linux-GNU-C.cmake"
  "/usr/share/cmake-3.16/Modules/Platform/Linux-GNU-CXX.cmake"
  "/usr/share/cmake-3.16/Modules/Platform/Linux-GNU.cmake"
  "/usr/share/cmake-3.16/Modules/Platform/Linux.cmake"
  "/usr/share/cmake-3.16/Modules/Platform/UnixPaths.cmake"
  )

# The corresponding makefile is:
set(CMAKE_MAKEFILE_OUTPUTS
  "Makefile"
  "CMakeFiles/cmake.check_cache"
  )

# Byproducts of CMake generate step:
set(CMAKE_MAKEFILE_PRODUCTS
  "CMakeFiles/run_sr_image_autogen.dir/AutogenInfo.json"
  "CMakeFiles/VideoSRLiteGUI_autogen.dir/AutogenInfo.json"
  "CMakeFiles/CMakeDirectoryInformation.cmake"
  )

# Dependency information for all targets:
set(CMAKE_DEPEND_INFO_FILES
  "CMakeFiles/run_sr_image.dir/DependInfo.cmake"
  "CMakeFiles/VideoSRLiteGUI.dir/DependInfo.cmake"
  "CMakeFiles/run_sr_image_autogen.dir/DependInfo.cmake"
  "CMakeFiles/VideoSRLiteGUI_autogen.dir/DependInfo.cmake"
  )
