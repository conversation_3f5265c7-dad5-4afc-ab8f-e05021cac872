# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.10

# The generator used is:
set(CMAKE_DEPENDS_GENERATOR "Unix Makefiles")

# The top level Makefile was generated from the following files:
set(CMAKE_MAKEFILE_DEPENDS
  "CMakeCache.txt"
  "../CMakeLists.txt"
  "CMakeFiles/3.10.2/CMakeCCompiler.cmake"
  "CMakeFiles/3.10.2/CMakeCXXCompiler.cmake"
  "CMakeFiles/3.10.2/CMakeSystem.cmake"
  "CMakeFiles/feature_tests.c"
  "CMakeFiles/feature_tests.cxx"
  "/opt/qt515/lib/cmake/Qt5/Qt5Config.cmake"
  "/opt/qt515/lib/cmake/Qt5/Qt5ConfigVersion.cmake"
  "/opt/qt515/lib/cmake/Qt5/Qt5ModuleLocation.cmake"
  "/opt/qt515/lib/cmake/Qt5Core/Qt5CoreConfig.cmake"
  "/opt/qt515/lib/cmake/Qt5Core/Qt5CoreConfigExtras.cmake"
  "/opt/qt515/lib/cmake/Qt5Core/Qt5CoreConfigExtrasMkspecDir.cmake"
  "/opt/qt515/lib/cmake/Qt5Core/Qt5CoreConfigVersion.cmake"
  "/opt/qt515/lib/cmake/Qt5Core/Qt5CoreMacros.cmake"
  "/opt/qt515/lib/cmake/Qt5Gui/Qt5GuiConfig.cmake"
  "/opt/qt515/lib/cmake/Qt5Gui/Qt5GuiConfigExtras.cmake"
  "/opt/qt515/lib/cmake/Qt5Gui/Qt5GuiConfigVersion.cmake"
  "/opt/qt515/lib/cmake/Qt5Gui/Qt5Gui_QComposePlatformInputContextPlugin.cmake"
  "/opt/qt515/lib/cmake/Qt5Gui/Qt5Gui_QEglFSEmulatorIntegrationPlugin.cmake"
  "/opt/qt515/lib/cmake/Qt5Gui/Qt5Gui_QEglFSIntegrationPlugin.cmake"
  "/opt/qt515/lib/cmake/Qt5Gui/Qt5Gui_QEglFSKmsEglDeviceIntegrationPlugin.cmake"
  "/opt/qt515/lib/cmake/Qt5Gui/Qt5Gui_QEglFSX11IntegrationPlugin.cmake"
  "/opt/qt515/lib/cmake/Qt5Gui/Qt5Gui_QEvdevKeyboardPlugin.cmake"
  "/opt/qt515/lib/cmake/Qt5Gui/Qt5Gui_QEvdevMousePlugin.cmake"
  "/opt/qt515/lib/cmake/Qt5Gui/Qt5Gui_QEvdevTabletPlugin.cmake"
  "/opt/qt515/lib/cmake/Qt5Gui/Qt5Gui_QEvdevTouchScreenPlugin.cmake"
  "/opt/qt515/lib/cmake/Qt5Gui/Qt5Gui_QGifPlugin.cmake"
  "/opt/qt515/lib/cmake/Qt5Gui/Qt5Gui_QGtk3ThemePlugin.cmake"
  "/opt/qt515/lib/cmake/Qt5Gui/Qt5Gui_QICOPlugin.cmake"
  "/opt/qt515/lib/cmake/Qt5Gui/Qt5Gui_QIbusPlatformInputContextPlugin.cmake"
  "/opt/qt515/lib/cmake/Qt5Gui/Qt5Gui_QJpegPlugin.cmake"
  "/opt/qt515/lib/cmake/Qt5Gui/Qt5Gui_QLinuxFbIntegrationPlugin.cmake"
  "/opt/qt515/lib/cmake/Qt5Gui/Qt5Gui_QMinimalEglIntegrationPlugin.cmake"
  "/opt/qt515/lib/cmake/Qt5Gui/Qt5Gui_QMinimalIntegrationPlugin.cmake"
  "/opt/qt515/lib/cmake/Qt5Gui/Qt5Gui_QOffscreenIntegrationPlugin.cmake"
  "/opt/qt515/lib/cmake/Qt5Gui/Qt5Gui_QTuioTouchPlugin.cmake"
  "/opt/qt515/lib/cmake/Qt5Gui/Qt5Gui_QVncIntegrationPlugin.cmake"
  "/opt/qt515/lib/cmake/Qt5Gui/Qt5Gui_QXcbEglIntegrationPlugin.cmake"
  "/opt/qt515/lib/cmake/Qt5Gui/Qt5Gui_QXcbGlxIntegrationPlugin.cmake"
  "/opt/qt515/lib/cmake/Qt5Gui/Qt5Gui_QXcbIntegrationPlugin.cmake"
  "/opt/qt515/lib/cmake/Qt5Gui/Qt5Gui_QXdgDesktopPortalThemePlugin.cmake"
  "/opt/qt515/lib/cmake/Qt5Widgets/Qt5WidgetsConfig.cmake"
  "/opt/qt515/lib/cmake/Qt5Widgets/Qt5WidgetsConfigExtras.cmake"
  "/opt/qt515/lib/cmake/Qt5Widgets/Qt5WidgetsConfigVersion.cmake"
  "/opt/qt515/lib/cmake/Qt5Widgets/Qt5WidgetsMacros.cmake"
  "/usr/share/OpenCV/OpenCVConfig-version.cmake"
  "/usr/share/OpenCV/OpenCVConfig.cmake"
  "/usr/share/OpenCV/OpenCVModules-release.cmake"
  "/usr/share/OpenCV/OpenCVModules.cmake"
  "/usr/share/cmake-3.10/Modules/AutogenInfo.cmake.in"
  "/usr/share/cmake-3.10/Modules/CMakeCCompiler.cmake.in"
  "/usr/share/cmake-3.10/Modules/CMakeCCompilerABI.c"
  "/usr/share/cmake-3.10/Modules/CMakeCInformation.cmake"
  "/usr/share/cmake-3.10/Modules/CMakeCXXCompiler.cmake.in"
  "/usr/share/cmake-3.10/Modules/CMakeCXXCompilerABI.cpp"
  "/usr/share/cmake-3.10/Modules/CMakeCXXInformation.cmake"
  "/usr/share/cmake-3.10/Modules/CMakeCommonLanguageInclude.cmake"
  "/usr/share/cmake-3.10/Modules/CMakeCompilerIdDetection.cmake"
  "/usr/share/cmake-3.10/Modules/CMakeConfigurableFile.in"
  "/usr/share/cmake-3.10/Modules/CMakeDetermineCCompiler.cmake"
  "/usr/share/cmake-3.10/Modules/CMakeDetermineCXXCompiler.cmake"
  "/usr/share/cmake-3.10/Modules/CMakeDetermineCompileFeatures.cmake"
  "/usr/share/cmake-3.10/Modules/CMakeDetermineCompiler.cmake"
  "/usr/share/cmake-3.10/Modules/CMakeDetermineCompilerABI.cmake"
  "/usr/share/cmake-3.10/Modules/CMakeDetermineCompilerId.cmake"
  "/usr/share/cmake-3.10/Modules/CMakeDetermineSystem.cmake"
  "/usr/share/cmake-3.10/Modules/CMakeFindBinUtils.cmake"
  "/usr/share/cmake-3.10/Modules/CMakeGenericSystem.cmake"
  "/usr/share/cmake-3.10/Modules/CMakeLanguageInformation.cmake"
  "/usr/share/cmake-3.10/Modules/CMakeParseArguments.cmake"
  "/usr/share/cmake-3.10/Modules/CMakeParseImplicitLinkInfo.cmake"
  "/usr/share/cmake-3.10/Modules/CMakeSystem.cmake.in"
  "/usr/share/cmake-3.10/Modules/CMakeSystemSpecificInformation.cmake"
  "/usr/share/cmake-3.10/Modules/CMakeSystemSpecificInitialize.cmake"
  "/usr/share/cmake-3.10/Modules/CMakeTestCCompiler.cmake"
  "/usr/share/cmake-3.10/Modules/CMakeTestCXXCompiler.cmake"
  "/usr/share/cmake-3.10/Modules/CMakeTestCompilerCommon.cmake"
  "/usr/share/cmake-3.10/Modules/CMakeUnixFindMake.cmake"
  "/usr/share/cmake-3.10/Modules/CheckFunctionExists.c"
  "/usr/share/cmake-3.10/Modules/CheckIncludeFile.c.in"
  "/usr/share/cmake-3.10/Modules/CheckIncludeFile.cmake"
  "/usr/share/cmake-3.10/Modules/CheckLibraryExists.cmake"
  "/usr/share/cmake-3.10/Modules/CheckSymbolExists.cmake"
  "/usr/share/cmake-3.10/Modules/Compiler/ADSP-DetermineCompiler.cmake"
  "/usr/share/cmake-3.10/Modules/Compiler/ARMCC-DetermineCompiler.cmake"
  "/usr/share/cmake-3.10/Modules/Compiler/AppleClang-DetermineCompiler.cmake"
  "/usr/share/cmake-3.10/Modules/Compiler/Borland-DetermineCompiler.cmake"
  "/usr/share/cmake-3.10/Modules/Compiler/Bruce-C-DetermineCompiler.cmake"
  "/usr/share/cmake-3.10/Modules/Compiler/CMakeCommonCompilerMacros.cmake"
  "/usr/share/cmake-3.10/Modules/Compiler/Clang-DetermineCompiler.cmake"
  "/usr/share/cmake-3.10/Modules/Compiler/Clang-DetermineCompilerInternal.cmake"
  "/usr/share/cmake-3.10/Modules/Compiler/Comeau-CXX-DetermineCompiler.cmake"
  "/usr/share/cmake-3.10/Modules/Compiler/Compaq-C-DetermineCompiler.cmake"
  "/usr/share/cmake-3.10/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake"
  "/usr/share/cmake-3.10/Modules/Compiler/Cray-DetermineCompiler.cmake"
  "/usr/share/cmake-3.10/Modules/Compiler/Embarcadero-DetermineCompiler.cmake"
  "/usr/share/cmake-3.10/Modules/Compiler/Fujitsu-DetermineCompiler.cmake"
  "/usr/share/cmake-3.10/Modules/Compiler/GHS-DetermineCompiler.cmake"
  "/usr/share/cmake-3.10/Modules/Compiler/GNU-C-DetermineCompiler.cmake"
  "/usr/share/cmake-3.10/Modules/Compiler/GNU-C-FeatureTests.cmake"
  "/usr/share/cmake-3.10/Modules/Compiler/GNU-C.cmake"
  "/usr/share/cmake-3.10/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake"
  "/usr/share/cmake-3.10/Modules/Compiler/GNU-CXX-FeatureTests.cmake"
  "/usr/share/cmake-3.10/Modules/Compiler/GNU-CXX.cmake"
  "/usr/share/cmake-3.10/Modules/Compiler/GNU-FindBinUtils.cmake"
  "/usr/share/cmake-3.10/Modules/Compiler/GNU.cmake"
  "/usr/share/cmake-3.10/Modules/Compiler/HP-C-DetermineCompiler.cmake"
  "/usr/share/cmake-3.10/Modules/Compiler/HP-CXX-DetermineCompiler.cmake"
  "/usr/share/cmake-3.10/Modules/Compiler/IAR-DetermineCompiler.cmake"
  "/usr/share/cmake-3.10/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake"
  "/usr/share/cmake-3.10/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake"
  "/usr/share/cmake-3.10/Modules/Compiler/Intel-DetermineCompiler.cmake"
  "/usr/share/cmake-3.10/Modules/Compiler/MIPSpro-DetermineCompiler.cmake"
  "/usr/share/cmake-3.10/Modules/Compiler/MSVC-DetermineCompiler.cmake"
  "/usr/share/cmake-3.10/Modules/Compiler/NVIDIA-DetermineCompiler.cmake"
  "/usr/share/cmake-3.10/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake"
  "/usr/share/cmake-3.10/Modules/Compiler/PGI-DetermineCompiler.cmake"
  "/usr/share/cmake-3.10/Modules/Compiler/PathScale-DetermineCompiler.cmake"
  "/usr/share/cmake-3.10/Modules/Compiler/SCO-DetermineCompiler.cmake"
  "/usr/share/cmake-3.10/Modules/Compiler/SDCC-C-DetermineCompiler.cmake"
  "/usr/share/cmake-3.10/Modules/Compiler/SunPro-C-DetermineCompiler.cmake"
  "/usr/share/cmake-3.10/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake"
  "/usr/share/cmake-3.10/Modules/Compiler/TI-DetermineCompiler.cmake"
  "/usr/share/cmake-3.10/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake"
  "/usr/share/cmake-3.10/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake"
  "/usr/share/cmake-3.10/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake"
  "/usr/share/cmake-3.10/Modules/Compiler/Watcom-DetermineCompiler.cmake"
  "/usr/share/cmake-3.10/Modules/Compiler/XL-C-DetermineCompiler.cmake"
  "/usr/share/cmake-3.10/Modules/Compiler/XL-CXX-DetermineCompiler.cmake"
  "/usr/share/cmake-3.10/Modules/Compiler/zOS-C-DetermineCompiler.cmake"
  "/usr/share/cmake-3.10/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake"
  "/usr/share/cmake-3.10/Modules/FindPackageHandleStandardArgs.cmake"
  "/usr/share/cmake-3.10/Modules/FindPackageMessage.cmake"
  "/usr/share/cmake-3.10/Modules/FindPkgConfig.cmake"
  "/usr/share/cmake-3.10/Modules/FindThreads.cmake"
  "/usr/share/cmake-3.10/Modules/Internal/FeatureTesting.cmake"
  "/usr/share/cmake-3.10/Modules/Platform/Linux-Determine-CXX.cmake"
  "/usr/share/cmake-3.10/Modules/Platform/Linux-GNU-C.cmake"
  "/usr/share/cmake-3.10/Modules/Platform/Linux-GNU-CXX.cmake"
  "/usr/share/cmake-3.10/Modules/Platform/Linux-GNU.cmake"
  "/usr/share/cmake-3.10/Modules/Platform/Linux.cmake"
  "/usr/share/cmake-3.10/Modules/Platform/UnixPaths.cmake"
  )

# The corresponding makefile is:
set(CMAKE_MAKEFILE_OUTPUTS
  "Makefile"
  "CMakeFiles/cmake.check_cache"
  )

# Byproducts of CMake generate step:
set(CMAKE_MAKEFILE_PRODUCTS
  "CMakeFiles/3.10.2/CMakeSystem.cmake"
  "CMakeFiles/3.10.2/CMakeCCompiler.cmake"
  "CMakeFiles/3.10.2/CMakeCXXCompiler.cmake"
  "CMakeFiles/3.10.2/CMakeCCompiler.cmake"
  "CMakeFiles/3.10.2/CMakeCXXCompiler.cmake"
  "CMakeFiles/run_sr_image_autogen.dir/AutogenInfo.cmake"
  "CMakeFiles/VideoSRLiteGUI_autogen.dir/AutogenInfo.cmake"
  "CMakeFiles/CMakeDirectoryInformation.cmake"
  )

# Dependency information for all targets:
set(CMAKE_DEPEND_INFO_FILES
  "CMakeFiles/run_sr_image.dir/DependInfo.cmake"
  "CMakeFiles/VideoSRLiteGUI.dir/DependInfo.cmake"
  "CMakeFiles/run_sr_image_autogen.dir/DependInfo.cmake"
  "CMakeFiles/VideoSRLiteGUI_autogen.dir/DependInfo.cmake"
  )
