# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.10

# The generator used is:
set(CMAKE_DEPENDS_GENERATOR "Unix Makefiles")

# The top level Makefile was generated from the following files:
set(CMAKE_MAKEFILE_DEPENDS
  "CMakeCache.txt"
  "../CMakeLists.txt"
  "CMakeFiles/3.10.2/CMakeCCompiler.cmake"
  "CMakeFiles/3.10.2/CMakeCXXCompiler.cmake"
  "CMakeFiles/3.10.2/CMakeSystem.cmake"
  "/opt/qt515/lib/cmake/Qt5/Qt5Config.cmake"
  "/opt/qt515/lib/cmake/Qt5/Qt5ConfigVersion.cmake"
  "/opt/qt515/lib/cmake/Qt5/Qt5ModuleLocation.cmake"
  "/opt/qt515/lib/cmake/Qt5Core/Qt5CoreConfig.cmake"
  "/opt/qt515/lib/cmake/Qt5Core/Qt5CoreConfigExtras.cmake"
  "/opt/qt515/lib/cmake/Qt5Core/Qt5CoreConfigExtrasMkspecDir.cmake"
  "/opt/qt515/lib/cmake/Qt5Core/Qt5CoreConfigVersion.cmake"
  "/opt/qt515/lib/cmake/Qt5Core/Qt5CoreMacros.cmake"
  "/opt/qt515/lib/cmake/Qt5Gui/Qt5GuiConfig.cmake"
  "/opt/qt515/lib/cmake/Qt5Gui/Qt5GuiConfigExtras.cmake"
  "/opt/qt515/lib/cmake/Qt5Gui/Qt5GuiConfigVersion.cmake"
  "/opt/qt515/lib/cmake/Qt5Gui/Qt5Gui_QComposePlatformInputContextPlugin.cmake"
  "/opt/qt515/lib/cmake/Qt5Gui/Qt5Gui_QEglFSEmulatorIntegrationPlugin.cmake"
  "/opt/qt515/lib/cmake/Qt5Gui/Qt5Gui_QEglFSIntegrationPlugin.cmake"
  "/opt/qt515/lib/cmake/Qt5Gui/Qt5Gui_QEglFSKmsEglDeviceIntegrationPlugin.cmake"
  "/opt/qt515/lib/cmake/Qt5Gui/Qt5Gui_QEglFSX11IntegrationPlugin.cmake"
  "/opt/qt515/lib/cmake/Qt5Gui/Qt5Gui_QEvdevKeyboardPlugin.cmake"
  "/opt/qt515/lib/cmake/Qt5Gui/Qt5Gui_QEvdevMousePlugin.cmake"
  "/opt/qt515/lib/cmake/Qt5Gui/Qt5Gui_QEvdevTabletPlugin.cmake"
  "/opt/qt515/lib/cmake/Qt5Gui/Qt5Gui_QEvdevTouchScreenPlugin.cmake"
  "/opt/qt515/lib/cmake/Qt5Gui/Qt5Gui_QGifPlugin.cmake"
  "/opt/qt515/lib/cmake/Qt5Gui/Qt5Gui_QGtk3ThemePlugin.cmake"
  "/opt/qt515/lib/cmake/Qt5Gui/Qt5Gui_QICOPlugin.cmake"
  "/opt/qt515/lib/cmake/Qt5Gui/Qt5Gui_QIbusPlatformInputContextPlugin.cmake"
  "/opt/qt515/lib/cmake/Qt5Gui/Qt5Gui_QJpegPlugin.cmake"
  "/opt/qt515/lib/cmake/Qt5Gui/Qt5Gui_QLinuxFbIntegrationPlugin.cmake"
  "/opt/qt515/lib/cmake/Qt5Gui/Qt5Gui_QMinimalEglIntegrationPlugin.cmake"
  "/opt/qt515/lib/cmake/Qt5Gui/Qt5Gui_QMinimalIntegrationPlugin.cmake"
  "/opt/qt515/lib/cmake/Qt5Gui/Qt5Gui_QOffscreenIntegrationPlugin.cmake"
  "/opt/qt515/lib/cmake/Qt5Gui/Qt5Gui_QTuioTouchPlugin.cmake"
  "/opt/qt515/lib/cmake/Qt5Gui/Qt5Gui_QVncIntegrationPlugin.cmake"
  "/opt/qt515/lib/cmake/Qt5Gui/Qt5Gui_QXcbEglIntegrationPlugin.cmake"
  "/opt/qt515/lib/cmake/Qt5Gui/Qt5Gui_QXcbGlxIntegrationPlugin.cmake"
  "/opt/qt515/lib/cmake/Qt5Gui/Qt5Gui_QXcbIntegrationPlugin.cmake"
  "/opt/qt515/lib/cmake/Qt5Gui/Qt5Gui_QXdgDesktopPortalThemePlugin.cmake"
  "/opt/qt515/lib/cmake/Qt5Widgets/Qt5WidgetsConfig.cmake"
  "/opt/qt515/lib/cmake/Qt5Widgets/Qt5WidgetsConfigExtras.cmake"
  "/opt/qt515/lib/cmake/Qt5Widgets/Qt5WidgetsConfigVersion.cmake"
  "/opt/qt515/lib/cmake/Qt5Widgets/Qt5WidgetsMacros.cmake"
  "/usr/share/OpenCV/OpenCVConfig-version.cmake"
  "/usr/share/OpenCV/OpenCVConfig.cmake"
  "/usr/share/OpenCV/OpenCVModules-release.cmake"
  "/usr/share/OpenCV/OpenCVModules.cmake"
  "/usr/share/cmake-3.10/Modules/AutogenInfo.cmake.in"
  "/usr/share/cmake-3.10/Modules/CMakeCInformation.cmake"
  "/usr/share/cmake-3.10/Modules/CMakeCXXInformation.cmake"
  "/usr/share/cmake-3.10/Modules/CMakeCommonLanguageInclude.cmake"
  "/usr/share/cmake-3.10/Modules/CMakeGenericSystem.cmake"
  "/usr/share/cmake-3.10/Modules/CMakeLanguageInformation.cmake"
  "/usr/share/cmake-3.10/Modules/CMakeParseArguments.cmake"
  "/usr/share/cmake-3.10/Modules/CMakeSystemSpecificInformation.cmake"
  "/usr/share/cmake-3.10/Modules/CMakeSystemSpecificInitialize.cmake"
  "/usr/share/cmake-3.10/Modules/CheckIncludeFile.cmake"
  "/usr/share/cmake-3.10/Modules/CheckLibraryExists.cmake"
  "/usr/share/cmake-3.10/Modules/CheckSymbolExists.cmake"
  "/usr/share/cmake-3.10/Modules/Compiler/CMakeCommonCompilerMacros.cmake"
  "/usr/share/cmake-3.10/Modules/Compiler/GNU-C.cmake"
  "/usr/share/cmake-3.10/Modules/Compiler/GNU-CXX.cmake"
  "/usr/share/cmake-3.10/Modules/Compiler/GNU.cmake"
  "/usr/share/cmake-3.10/Modules/FindPackageHandleStandardArgs.cmake"
  "/usr/share/cmake-3.10/Modules/FindPackageMessage.cmake"
  "/usr/share/cmake-3.10/Modules/FindPkgConfig.cmake"
  "/usr/share/cmake-3.10/Modules/FindThreads.cmake"
  "/usr/share/cmake-3.10/Modules/Platform/Linux-GNU-C.cmake"
  "/usr/share/cmake-3.10/Modules/Platform/Linux-GNU-CXX.cmake"
  "/usr/share/cmake-3.10/Modules/Platform/Linux-GNU.cmake"
  "/usr/share/cmake-3.10/Modules/Platform/Linux.cmake"
  "/usr/share/cmake-3.10/Modules/Platform/UnixPaths.cmake"
  )

# The corresponding makefile is:
set(CMAKE_MAKEFILE_OUTPUTS
  "Makefile"
  "CMakeFiles/cmake.check_cache"
  )

# Byproducts of CMake generate step:
set(CMAKE_MAKEFILE_PRODUCTS
  "CMakeFiles/run_sr_image_autogen.dir/AutogenInfo.cmake"
  "CMakeFiles/VideoSRLiteGUI_autogen.dir/AutogenInfo.cmake"
  "CMakeFiles/CMakeDirectoryInformation.cmake"
  )

# Dependency information for all targets:
set(CMAKE_DEPEND_INFO_FILES
  "CMakeFiles/run_sr_image.dir/DependInfo.cmake"
  "CMakeFiles/VideoSRLiteGUI.dir/DependInfo.cmake"
  "CMakeFiles/run_sr_image_autogen.dir/DependInfo.cmake"
  "CMakeFiles/VideoSRLiteGUI_autogen.dir/DependInfo.cmake"
  )
