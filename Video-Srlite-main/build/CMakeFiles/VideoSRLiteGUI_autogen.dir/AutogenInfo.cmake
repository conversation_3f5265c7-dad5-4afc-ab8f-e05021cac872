# Meta
set(AM_MULTI_CONFIG "SINGLE")
# Directories and files
set(AM_CMAKE_BINARY_DIR "/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build/")
set(AM_CMAKE_SOURCE_DIR "/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/")
set(AM_CMAKE_CURRENT_SOURCE_DIR "/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/")
set(AM_CMAKE_CURRENT_BINARY_DIR "/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build/")
set(AM_CMAKE_INCLUDE_DIRECTORIES_PROJECT_BEFORE "")
set(AM_BUILD_DIR "/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build/VideoSRLiteGUI_autogen")
set(AM_SOURCES "/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/AppController/AppController.cpp;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/AppController/WorkerPool.cpp;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/AudioProc/AudioProc.cpp;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/AudioProcessor/AudioProcessor.cpp;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Decoder/src/AudioDecoder.cpp;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Decoder/src/Decoder.cpp;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Decoder/src/VideoDecoder.cpp;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Encoder/AudioEncoder.cpp;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Encoder/Encoder.cpp;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Encoder/Muxer.cpp;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Encoder/VideoEncoder.cpp;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/PostFilter/PostFilter.cpp;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/PostFilter/PostFilterProcessor.cpp;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Processing/AudioDenoiser.cpp;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Processing/PostProcessor.cpp;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Processing/SuperResolution.cpp;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/SuperEigen/src/ModelSession.cpp;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/SuperEigen/src/PrePostProcessor.cpp;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/SuperEigen/src/SuperResConfig.cpp;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/SuperEigen/src/SuperResEngine.cpp;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/SyncVA/AVSyncManager.cpp;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/UI/FileListWidget.cpp;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/UI/ImagePreviewWidget.cpp;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/UI/ImageProcessor.cpp;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/UI/MainWindow.cpp;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/UI/SettingsPanel.cpp;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Utils/FileUtils.cpp;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Utils/LogUtils.cpp;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Utils/Logger.cpp;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/WorkerPool/WorkerPool.cpp;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/main.cpp")
set(AM_HEADERS "/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/AppController/AppController.h;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/AppController/WorkerPool.h;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/AudioProc/AudioProc.h;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/AudioProcessor/AudioProcessor.h;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/DataStruct/AudioFrameData.h;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/DataStruct/FrameData.h;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Decoder/include/AudioDecoder.h;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Decoder/include/Decoder.h;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Decoder/include/VideoDecoder.h;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Encoder/AudioEncoder.h;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Encoder/Encoder.h;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Encoder/Muxer.h;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Encoder/VideoEncoder.h;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/PostFilter/PostFilter.h;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/PostFilter/PostFilterProcessor.h;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Processing/AudioDenoiser.h;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Processing/PostProcessor.h;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Processing/SuperResolution.h;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/SuperEigen/include/ModelSession.h;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/SuperEigen/include/PrePostProcessor.h;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/SuperEigen/include/SuperResConfig.h;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/SuperEigen/include/SuperResEngine.h;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/SyncVA/AVSyncManager.h;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/UI/FileListWidget.h;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/UI/ImagePreviewWidget.h;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/UI/ImageProcessor.h;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/UI/MainWindow.h;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/UI/SettingsPanel.h;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Utils/FileUtils.h;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Utils/LogUtils.h;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Utils/Logger.h;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/WorkerPool/WorkerPool.h")
# Qt environment
set(AM_QT_VERSION_MAJOR "5")
set(AM_QT_VERSION_MINOR "15")
set(AM_QT_MOC_EXECUTABLE "/opt/qt515/bin/moc")
set(AM_QT_UIC_EXECUTABLE "/opt/qt515/bin/uic")
set(AM_QT_RCC_EXECUTABLE "/opt/qt515/bin/rcc")
# MOC settings
set(AM_MOC_SKIP "/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build/VideoSRLiteGUI_autogen/mocs_compilation.cpp;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build/run_sr_image_autogen/mocs_compilation.cpp")
set(AM_MOC_DEFINITIONS "HAVE_ONNXRUNTIME;QT_CORE_LIB;QT_GUI_LIB;QT_NO_DEBUG;QT_WIDGETS_LIB")
set(AM_MOC_INCLUDES "/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build/VideoSRLiteGUI_autogen/include;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Processing;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/UI;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Utils;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/DataStruct;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Decoder/include;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/SuperEigen/include;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/AppController;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/AudioProcessor;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/AudioProc;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Encoder;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/SyncVA;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/PostFilter;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/WorkerPool;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/Core;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/onnx/onnxruntime-linux-x64-1.15.1/include;/usr/include/x86_64-linux-gnu;/usr/include/opencv;/opt/qt515/include;/opt/qt515/include/QtCore;/opt/qt515/./mkspecs/linux-g++;/opt/qt515/include/QtWidgets;/opt/qt515/include/QtGui;/usr/include")
set(AM_MOC_OPTIONS "")
set(AM_MOC_RELAXED_MODE "FALSE")
set(AM_MOC_MACRO_NAMES "Q_OBJECT;Q_GADGET;Q_NAMESPACE;Q_NAMESPACE_EXPORT")
set(AM_MOC_DEPEND_FILTERS "")
set(AM_MOC_PREDEFS_CMD "/usr/bin/c++;-dM;-E;-c;/usr/share/cmake-3.10/Modules/CMakeCXXCompilerABI.cpp")
# UIC settings
set(AM_UIC_SKIP "/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build/VideoSRLiteGUI_autogen/mocs_compilation.cpp;/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build/run_sr_image_autogen/mocs_compilation.cpp")
set(AM_UIC_TARGET_OPTIONS "")
set(AM_UIC_OPTIONS_FILES "")
set(AM_UIC_OPTIONS_OPTIONS "")
set(AM_UIC_SEARCH_PATHS "/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/src/UI")
# RCC settings
set(AM_RCC_SOURCES "")
set(AM_RCC_BUILDS "")
set(AM_RCC_OPTIONS "")
set(AM_RCC_INPUTS "")
# Configurations options
set(AM_CONFIG_SUFFIX_Release "_Release")
