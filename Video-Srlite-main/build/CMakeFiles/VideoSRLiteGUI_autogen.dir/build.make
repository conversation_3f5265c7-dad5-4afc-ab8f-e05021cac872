# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.10

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build

# Utility rule file for VideoSRLiteGUI_autogen.

# Include the progress variables for this target.
include CMakeFiles/VideoSRLiteGUI_autogen.dir/progress.make

CMakeFiles/VideoSRLiteGUI_autogen:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --blue --bold --progress-dir=/home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Automatic MOC, UIC and RCC for target VideoSRLiteGUI"
	/usr/bin/cmake -E cmake_autogen /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build/CMakeFiles/VideoSRLiteGUI_autogen.dir Release

VideoSRLiteGUI_autogen: CMakeFiles/VideoSRLiteGUI_autogen
VideoSRLiteGUI_autogen: CMakeFiles/VideoSRLiteGUI_autogen.dir/build.make

.PHONY : VideoSRLiteGUI_autogen

# Rule to build all files generated by this target.
CMakeFiles/VideoSRLiteGUI_autogen.dir/build: VideoSRLiteGUI_autogen

.PHONY : CMakeFiles/VideoSRLiteGUI_autogen.dir/build

CMakeFiles/VideoSRLiteGUI_autogen.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/VideoSRLiteGUI_autogen.dir/cmake_clean.cmake
.PHONY : CMakeFiles/VideoSRLiteGUI_autogen.dir/clean

CMakeFiles/VideoSRLiteGUI_autogen.dir/depend:
	cd /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build /home/<USER>/桌面/Vedio_SRLITE/Video-Srlite-main/build/CMakeFiles/VideoSRLiteGUI_autogen.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/VideoSRLiteGUI_autogen.dir/depend

