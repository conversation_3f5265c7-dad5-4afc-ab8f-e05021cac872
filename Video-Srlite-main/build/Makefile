# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Default target executed when no arguments are given to make.
default_target: all

.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Public/video_lite/VideoSR-Lite

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Public/video_lite/VideoSR-Lite/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components

.PHONY : list_install_components/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache

.PHONY : rebuild_cache/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache

.PHONY : edit_cache/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Public/video_lite/VideoSR-Lite/build/CMakeFiles /home/<USER>/Public/video_lite/VideoSR-Lite/build/CMakeFiles/progress.marks
	$(MAKE) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Public/video_lite/VideoSR-Lite/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean

.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named run_sr_image

# Build rule for target.
run_sr_image: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 run_sr_image
.PHONY : run_sr_image

# fast build rule for target.
run_sr_image/fast:
	$(MAKE) -f CMakeFiles/run_sr_image.dir/build.make CMakeFiles/run_sr_image.dir/build
.PHONY : run_sr_image/fast

#=============================================================================
# Target rules for targets named VideoSRLiteGUI

# Build rule for target.
VideoSRLiteGUI: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 VideoSRLiteGUI
.PHONY : VideoSRLiteGUI

# fast build rule for target.
VideoSRLiteGUI/fast:
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/build
.PHONY : VideoSRLiteGUI/fast

#=============================================================================
# Target rules for targets named run_sr_image_autogen

# Build rule for target.
run_sr_image_autogen: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 run_sr_image_autogen
.PHONY : run_sr_image_autogen

# fast build rule for target.
run_sr_image_autogen/fast:
	$(MAKE) -f CMakeFiles/run_sr_image_autogen.dir/build.make CMakeFiles/run_sr_image_autogen.dir/build
.PHONY : run_sr_image_autogen/fast

#=============================================================================
# Target rules for targets named VideoSRLiteGUI_autogen

# Build rule for target.
VideoSRLiteGUI_autogen: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 VideoSRLiteGUI_autogen
.PHONY : VideoSRLiteGUI_autogen

# fast build rule for target.
VideoSRLiteGUI_autogen/fast:
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI_autogen.dir/build.make CMakeFiles/VideoSRLiteGUI_autogen.dir/build
.PHONY : VideoSRLiteGUI_autogen/fast

VideoSRLiteGUI_autogen/mocs_compilation.o: VideoSRLiteGUI_autogen/mocs_compilation.cpp.o

.PHONY : VideoSRLiteGUI_autogen/mocs_compilation.o

# target to build an object file
VideoSRLiteGUI_autogen/mocs_compilation.cpp.o:
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.o
.PHONY : VideoSRLiteGUI_autogen/mocs_compilation.cpp.o

VideoSRLiteGUI_autogen/mocs_compilation.i: VideoSRLiteGUI_autogen/mocs_compilation.cpp.i

.PHONY : VideoSRLiteGUI_autogen/mocs_compilation.i

# target to preprocess a source file
VideoSRLiteGUI_autogen/mocs_compilation.cpp.i:
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.i
.PHONY : VideoSRLiteGUI_autogen/mocs_compilation.cpp.i

VideoSRLiteGUI_autogen/mocs_compilation.s: VideoSRLiteGUI_autogen/mocs_compilation.cpp.s

.PHONY : VideoSRLiteGUI_autogen/mocs_compilation.s

# target to generate assembly for a file
VideoSRLiteGUI_autogen/mocs_compilation.cpp.s:
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/VideoSRLiteGUI_autogen/mocs_compilation.cpp.s
.PHONY : VideoSRLiteGUI_autogen/mocs_compilation.cpp.s

run_sr_image_autogen/mocs_compilation.o: run_sr_image_autogen/mocs_compilation.cpp.o

.PHONY : run_sr_image_autogen/mocs_compilation.o

# target to build an object file
run_sr_image_autogen/mocs_compilation.cpp.o:
	$(MAKE) -f CMakeFiles/run_sr_image.dir/build.make CMakeFiles/run_sr_image.dir/run_sr_image_autogen/mocs_compilation.cpp.o
.PHONY : run_sr_image_autogen/mocs_compilation.cpp.o

run_sr_image_autogen/mocs_compilation.i: run_sr_image_autogen/mocs_compilation.cpp.i

.PHONY : run_sr_image_autogen/mocs_compilation.i

# target to preprocess a source file
run_sr_image_autogen/mocs_compilation.cpp.i:
	$(MAKE) -f CMakeFiles/run_sr_image.dir/build.make CMakeFiles/run_sr_image.dir/run_sr_image_autogen/mocs_compilation.cpp.i
.PHONY : run_sr_image_autogen/mocs_compilation.cpp.i

run_sr_image_autogen/mocs_compilation.s: run_sr_image_autogen/mocs_compilation.cpp.s

.PHONY : run_sr_image_autogen/mocs_compilation.s

# target to generate assembly for a file
run_sr_image_autogen/mocs_compilation.cpp.s:
	$(MAKE) -f CMakeFiles/run_sr_image.dir/build.make CMakeFiles/run_sr_image.dir/run_sr_image_autogen/mocs_compilation.cpp.s
.PHONY : run_sr_image_autogen/mocs_compilation.cpp.s

src/AppController/AppController.o: src/AppController/AppController.cpp.o

.PHONY : src/AppController/AppController.o

# target to build an object file
src/AppController/AppController.cpp.o:
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/src/AppController/AppController.cpp.o
.PHONY : src/AppController/AppController.cpp.o

src/AppController/AppController.i: src/AppController/AppController.cpp.i

.PHONY : src/AppController/AppController.i

# target to preprocess a source file
src/AppController/AppController.cpp.i:
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/src/AppController/AppController.cpp.i
.PHONY : src/AppController/AppController.cpp.i

src/AppController/AppController.s: src/AppController/AppController.cpp.s

.PHONY : src/AppController/AppController.s

# target to generate assembly for a file
src/AppController/AppController.cpp.s:
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/src/AppController/AppController.cpp.s
.PHONY : src/AppController/AppController.cpp.s

src/AppController/WorkerPool.o: src/AppController/WorkerPool.cpp.o

.PHONY : src/AppController/WorkerPool.o

# target to build an object file
src/AppController/WorkerPool.cpp.o:
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/src/AppController/WorkerPool.cpp.o
.PHONY : src/AppController/WorkerPool.cpp.o

src/AppController/WorkerPool.i: src/AppController/WorkerPool.cpp.i

.PHONY : src/AppController/WorkerPool.i

# target to preprocess a source file
src/AppController/WorkerPool.cpp.i:
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/src/AppController/WorkerPool.cpp.i
.PHONY : src/AppController/WorkerPool.cpp.i

src/AppController/WorkerPool.s: src/AppController/WorkerPool.cpp.s

.PHONY : src/AppController/WorkerPool.s

# target to generate assembly for a file
src/AppController/WorkerPool.cpp.s:
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/src/AppController/WorkerPool.cpp.s
.PHONY : src/AppController/WorkerPool.cpp.s

src/AudioProc/AudioProc.o: src/AudioProc/AudioProc.cpp.o

.PHONY : src/AudioProc/AudioProc.o

# target to build an object file
src/AudioProc/AudioProc.cpp.o:
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/src/AudioProc/AudioProc.cpp.o
.PHONY : src/AudioProc/AudioProc.cpp.o

src/AudioProc/AudioProc.i: src/AudioProc/AudioProc.cpp.i

.PHONY : src/AudioProc/AudioProc.i

# target to preprocess a source file
src/AudioProc/AudioProc.cpp.i:
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/src/AudioProc/AudioProc.cpp.i
.PHONY : src/AudioProc/AudioProc.cpp.i

src/AudioProc/AudioProc.s: src/AudioProc/AudioProc.cpp.s

.PHONY : src/AudioProc/AudioProc.s

# target to generate assembly for a file
src/AudioProc/AudioProc.cpp.s:
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/src/AudioProc/AudioProc.cpp.s
.PHONY : src/AudioProc/AudioProc.cpp.s

src/AudioProcessor/AudioProcessor.o: src/AudioProcessor/AudioProcessor.cpp.o

.PHONY : src/AudioProcessor/AudioProcessor.o

# target to build an object file
src/AudioProcessor/AudioProcessor.cpp.o:
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/src/AudioProcessor/AudioProcessor.cpp.o
.PHONY : src/AudioProcessor/AudioProcessor.cpp.o

src/AudioProcessor/AudioProcessor.i: src/AudioProcessor/AudioProcessor.cpp.i

.PHONY : src/AudioProcessor/AudioProcessor.i

# target to preprocess a source file
src/AudioProcessor/AudioProcessor.cpp.i:
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/src/AudioProcessor/AudioProcessor.cpp.i
.PHONY : src/AudioProcessor/AudioProcessor.cpp.i

src/AudioProcessor/AudioProcessor.s: src/AudioProcessor/AudioProcessor.cpp.s

.PHONY : src/AudioProcessor/AudioProcessor.s

# target to generate assembly for a file
src/AudioProcessor/AudioProcessor.cpp.s:
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/src/AudioProcessor/AudioProcessor.cpp.s
.PHONY : src/AudioProcessor/AudioProcessor.cpp.s

src/Decoder/src/AudioDecoder.o: src/Decoder/src/AudioDecoder.cpp.o

.PHONY : src/Decoder/src/AudioDecoder.o

# target to build an object file
src/Decoder/src/AudioDecoder.cpp.o:
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/AudioDecoder.cpp.o
.PHONY : src/Decoder/src/AudioDecoder.cpp.o

src/Decoder/src/AudioDecoder.i: src/Decoder/src/AudioDecoder.cpp.i

.PHONY : src/Decoder/src/AudioDecoder.i

# target to preprocess a source file
src/Decoder/src/AudioDecoder.cpp.i:
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/AudioDecoder.cpp.i
.PHONY : src/Decoder/src/AudioDecoder.cpp.i

src/Decoder/src/AudioDecoder.s: src/Decoder/src/AudioDecoder.cpp.s

.PHONY : src/Decoder/src/AudioDecoder.s

# target to generate assembly for a file
src/Decoder/src/AudioDecoder.cpp.s:
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/AudioDecoder.cpp.s
.PHONY : src/Decoder/src/AudioDecoder.cpp.s

src/Decoder/src/Decoder.o: src/Decoder/src/Decoder.cpp.o

.PHONY : src/Decoder/src/Decoder.o

# target to build an object file
src/Decoder/src/Decoder.cpp.o:
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/Decoder.cpp.o
.PHONY : src/Decoder/src/Decoder.cpp.o

src/Decoder/src/Decoder.i: src/Decoder/src/Decoder.cpp.i

.PHONY : src/Decoder/src/Decoder.i

# target to preprocess a source file
src/Decoder/src/Decoder.cpp.i:
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/Decoder.cpp.i
.PHONY : src/Decoder/src/Decoder.cpp.i

src/Decoder/src/Decoder.s: src/Decoder/src/Decoder.cpp.s

.PHONY : src/Decoder/src/Decoder.s

# target to generate assembly for a file
src/Decoder/src/Decoder.cpp.s:
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/Decoder.cpp.s
.PHONY : src/Decoder/src/Decoder.cpp.s

src/Decoder/src/VideoDecoder.o: src/Decoder/src/VideoDecoder.cpp.o

.PHONY : src/Decoder/src/VideoDecoder.o

# target to build an object file
src/Decoder/src/VideoDecoder.cpp.o:
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/VideoDecoder.cpp.o
.PHONY : src/Decoder/src/VideoDecoder.cpp.o

src/Decoder/src/VideoDecoder.i: src/Decoder/src/VideoDecoder.cpp.i

.PHONY : src/Decoder/src/VideoDecoder.i

# target to preprocess a source file
src/Decoder/src/VideoDecoder.cpp.i:
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/VideoDecoder.cpp.i
.PHONY : src/Decoder/src/VideoDecoder.cpp.i

src/Decoder/src/VideoDecoder.s: src/Decoder/src/VideoDecoder.cpp.s

.PHONY : src/Decoder/src/VideoDecoder.s

# target to generate assembly for a file
src/Decoder/src/VideoDecoder.cpp.s:
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/src/Decoder/src/VideoDecoder.cpp.s
.PHONY : src/Decoder/src/VideoDecoder.cpp.s

src/Encoder/AudioEncoder.o: src/Encoder/AudioEncoder.cpp.o

.PHONY : src/Encoder/AudioEncoder.o

# target to build an object file
src/Encoder/AudioEncoder.cpp.o:
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/AudioEncoder.cpp.o
.PHONY : src/Encoder/AudioEncoder.cpp.o

src/Encoder/AudioEncoder.i: src/Encoder/AudioEncoder.cpp.i

.PHONY : src/Encoder/AudioEncoder.i

# target to preprocess a source file
src/Encoder/AudioEncoder.cpp.i:
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/AudioEncoder.cpp.i
.PHONY : src/Encoder/AudioEncoder.cpp.i

src/Encoder/AudioEncoder.s: src/Encoder/AudioEncoder.cpp.s

.PHONY : src/Encoder/AudioEncoder.s

# target to generate assembly for a file
src/Encoder/AudioEncoder.cpp.s:
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/AudioEncoder.cpp.s
.PHONY : src/Encoder/AudioEncoder.cpp.s

src/Encoder/Encoder.o: src/Encoder/Encoder.cpp.o

.PHONY : src/Encoder/Encoder.o

# target to build an object file
src/Encoder/Encoder.cpp.o:
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.o
.PHONY : src/Encoder/Encoder.cpp.o

src/Encoder/Encoder.i: src/Encoder/Encoder.cpp.i

.PHONY : src/Encoder/Encoder.i

# target to preprocess a source file
src/Encoder/Encoder.cpp.i:
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.i
.PHONY : src/Encoder/Encoder.cpp.i

src/Encoder/Encoder.s: src/Encoder/Encoder.cpp.s

.PHONY : src/Encoder/Encoder.s

# target to generate assembly for a file
src/Encoder/Encoder.cpp.s:
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Encoder.cpp.s
.PHONY : src/Encoder/Encoder.cpp.s

src/Encoder/Muxer.o: src/Encoder/Muxer.cpp.o

.PHONY : src/Encoder/Muxer.o

# target to build an object file
src/Encoder/Muxer.cpp.o:
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Muxer.cpp.o
.PHONY : src/Encoder/Muxer.cpp.o

src/Encoder/Muxer.i: src/Encoder/Muxer.cpp.i

.PHONY : src/Encoder/Muxer.i

# target to preprocess a source file
src/Encoder/Muxer.cpp.i:
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Muxer.cpp.i
.PHONY : src/Encoder/Muxer.cpp.i

src/Encoder/Muxer.s: src/Encoder/Muxer.cpp.s

.PHONY : src/Encoder/Muxer.s

# target to generate assembly for a file
src/Encoder/Muxer.cpp.s:
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/Muxer.cpp.s
.PHONY : src/Encoder/Muxer.cpp.s

src/Encoder/VideoEncoder.o: src/Encoder/VideoEncoder.cpp.o

.PHONY : src/Encoder/VideoEncoder.o

# target to build an object file
src/Encoder/VideoEncoder.cpp.o:
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/VideoEncoder.cpp.o
.PHONY : src/Encoder/VideoEncoder.cpp.o

src/Encoder/VideoEncoder.i: src/Encoder/VideoEncoder.cpp.i

.PHONY : src/Encoder/VideoEncoder.i

# target to preprocess a source file
src/Encoder/VideoEncoder.cpp.i:
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/VideoEncoder.cpp.i
.PHONY : src/Encoder/VideoEncoder.cpp.i

src/Encoder/VideoEncoder.s: src/Encoder/VideoEncoder.cpp.s

.PHONY : src/Encoder/VideoEncoder.s

# target to generate assembly for a file
src/Encoder/VideoEncoder.cpp.s:
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/src/Encoder/VideoEncoder.cpp.s
.PHONY : src/Encoder/VideoEncoder.cpp.s

src/PostFilter/PostFilter.o: src/PostFilter/PostFilter.cpp.o

.PHONY : src/PostFilter/PostFilter.o

# target to build an object file
src/PostFilter/PostFilter.cpp.o:
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/src/PostFilter/PostFilter.cpp.o
.PHONY : src/PostFilter/PostFilter.cpp.o

src/PostFilter/PostFilter.i: src/PostFilter/PostFilter.cpp.i

.PHONY : src/PostFilter/PostFilter.i

# target to preprocess a source file
src/PostFilter/PostFilter.cpp.i:
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/src/PostFilter/PostFilter.cpp.i
.PHONY : src/PostFilter/PostFilter.cpp.i

src/PostFilter/PostFilter.s: src/PostFilter/PostFilter.cpp.s

.PHONY : src/PostFilter/PostFilter.s

# target to generate assembly for a file
src/PostFilter/PostFilter.cpp.s:
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/src/PostFilter/PostFilter.cpp.s
.PHONY : src/PostFilter/PostFilter.cpp.s

src/PostFilter/PostFilterProcessor.o: src/PostFilter/PostFilterProcessor.cpp.o

.PHONY : src/PostFilter/PostFilterProcessor.o

# target to build an object file
src/PostFilter/PostFilterProcessor.cpp.o:
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/src/PostFilter/PostFilterProcessor.cpp.o
.PHONY : src/PostFilter/PostFilterProcessor.cpp.o

src/PostFilter/PostFilterProcessor.i: src/PostFilter/PostFilterProcessor.cpp.i

.PHONY : src/PostFilter/PostFilterProcessor.i

# target to preprocess a source file
src/PostFilter/PostFilterProcessor.cpp.i:
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/src/PostFilter/PostFilterProcessor.cpp.i
.PHONY : src/PostFilter/PostFilterProcessor.cpp.i

src/PostFilter/PostFilterProcessor.s: src/PostFilter/PostFilterProcessor.cpp.s

.PHONY : src/PostFilter/PostFilterProcessor.s

# target to generate assembly for a file
src/PostFilter/PostFilterProcessor.cpp.s:
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/src/PostFilter/PostFilterProcessor.cpp.s
.PHONY : src/PostFilter/PostFilterProcessor.cpp.s

src/Processing/AudioDenoiser.o: src/Processing/AudioDenoiser.cpp.o

.PHONY : src/Processing/AudioDenoiser.o

# target to build an object file
src/Processing/AudioDenoiser.cpp.o:
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/src/Processing/AudioDenoiser.cpp.o
.PHONY : src/Processing/AudioDenoiser.cpp.o

src/Processing/AudioDenoiser.i: src/Processing/AudioDenoiser.cpp.i

.PHONY : src/Processing/AudioDenoiser.i

# target to preprocess a source file
src/Processing/AudioDenoiser.cpp.i:
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/src/Processing/AudioDenoiser.cpp.i
.PHONY : src/Processing/AudioDenoiser.cpp.i

src/Processing/AudioDenoiser.s: src/Processing/AudioDenoiser.cpp.s

.PHONY : src/Processing/AudioDenoiser.s

# target to generate assembly for a file
src/Processing/AudioDenoiser.cpp.s:
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/src/Processing/AudioDenoiser.cpp.s
.PHONY : src/Processing/AudioDenoiser.cpp.s

src/Processing/PostProcessor.o: src/Processing/PostProcessor.cpp.o

.PHONY : src/Processing/PostProcessor.o

# target to build an object file
src/Processing/PostProcessor.cpp.o:
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/src/Processing/PostProcessor.cpp.o
.PHONY : src/Processing/PostProcessor.cpp.o

src/Processing/PostProcessor.i: src/Processing/PostProcessor.cpp.i

.PHONY : src/Processing/PostProcessor.i

# target to preprocess a source file
src/Processing/PostProcessor.cpp.i:
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/src/Processing/PostProcessor.cpp.i
.PHONY : src/Processing/PostProcessor.cpp.i

src/Processing/PostProcessor.s: src/Processing/PostProcessor.cpp.s

.PHONY : src/Processing/PostProcessor.s

# target to generate assembly for a file
src/Processing/PostProcessor.cpp.s:
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/src/Processing/PostProcessor.cpp.s
.PHONY : src/Processing/PostProcessor.cpp.s

src/Processing/SuperResolution.o: src/Processing/SuperResolution.cpp.o

.PHONY : src/Processing/SuperResolution.o

# target to build an object file
src/Processing/SuperResolution.cpp.o:
	$(MAKE) -f CMakeFiles/run_sr_image.dir/build.make CMakeFiles/run_sr_image.dir/src/Processing/SuperResolution.cpp.o
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/src/Processing/SuperResolution.cpp.o
.PHONY : src/Processing/SuperResolution.cpp.o

src/Processing/SuperResolution.i: src/Processing/SuperResolution.cpp.i

.PHONY : src/Processing/SuperResolution.i

# target to preprocess a source file
src/Processing/SuperResolution.cpp.i:
	$(MAKE) -f CMakeFiles/run_sr_image.dir/build.make CMakeFiles/run_sr_image.dir/src/Processing/SuperResolution.cpp.i
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/src/Processing/SuperResolution.cpp.i
.PHONY : src/Processing/SuperResolution.cpp.i

src/Processing/SuperResolution.s: src/Processing/SuperResolution.cpp.s

.PHONY : src/Processing/SuperResolution.s

# target to generate assembly for a file
src/Processing/SuperResolution.cpp.s:
	$(MAKE) -f CMakeFiles/run_sr_image.dir/build.make CMakeFiles/run_sr_image.dir/src/Processing/SuperResolution.cpp.s
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/src/Processing/SuperResolution.cpp.s
.PHONY : src/Processing/SuperResolution.cpp.s

src/SuperEigen/src/ModelSession.o: src/SuperEigen/src/ModelSession.cpp.o

.PHONY : src/SuperEigen/src/ModelSession.o

# target to build an object file
src/SuperEigen/src/ModelSession.cpp.o:
	$(MAKE) -f CMakeFiles/run_sr_image.dir/build.make CMakeFiles/run_sr_image.dir/src/SuperEigen/src/ModelSession.cpp.o
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/src/SuperEigen/src/ModelSession.cpp.o
.PHONY : src/SuperEigen/src/ModelSession.cpp.o

src/SuperEigen/src/ModelSession.i: src/SuperEigen/src/ModelSession.cpp.i

.PHONY : src/SuperEigen/src/ModelSession.i

# target to preprocess a source file
src/SuperEigen/src/ModelSession.cpp.i:
	$(MAKE) -f CMakeFiles/run_sr_image.dir/build.make CMakeFiles/run_sr_image.dir/src/SuperEigen/src/ModelSession.cpp.i
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/src/SuperEigen/src/ModelSession.cpp.i
.PHONY : src/SuperEigen/src/ModelSession.cpp.i

src/SuperEigen/src/ModelSession.s: src/SuperEigen/src/ModelSession.cpp.s

.PHONY : src/SuperEigen/src/ModelSession.s

# target to generate assembly for a file
src/SuperEigen/src/ModelSession.cpp.s:
	$(MAKE) -f CMakeFiles/run_sr_image.dir/build.make CMakeFiles/run_sr_image.dir/src/SuperEigen/src/ModelSession.cpp.s
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/src/SuperEigen/src/ModelSession.cpp.s
.PHONY : src/SuperEigen/src/ModelSession.cpp.s

src/SuperEigen/src/PrePostProcessor.o: src/SuperEigen/src/PrePostProcessor.cpp.o

.PHONY : src/SuperEigen/src/PrePostProcessor.o

# target to build an object file
src/SuperEigen/src/PrePostProcessor.cpp.o:
	$(MAKE) -f CMakeFiles/run_sr_image.dir/build.make CMakeFiles/run_sr_image.dir/src/SuperEigen/src/PrePostProcessor.cpp.o
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/src/SuperEigen/src/PrePostProcessor.cpp.o
.PHONY : src/SuperEigen/src/PrePostProcessor.cpp.o

src/SuperEigen/src/PrePostProcessor.i: src/SuperEigen/src/PrePostProcessor.cpp.i

.PHONY : src/SuperEigen/src/PrePostProcessor.i

# target to preprocess a source file
src/SuperEigen/src/PrePostProcessor.cpp.i:
	$(MAKE) -f CMakeFiles/run_sr_image.dir/build.make CMakeFiles/run_sr_image.dir/src/SuperEigen/src/PrePostProcessor.cpp.i
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/src/SuperEigen/src/PrePostProcessor.cpp.i
.PHONY : src/SuperEigen/src/PrePostProcessor.cpp.i

src/SuperEigen/src/PrePostProcessor.s: src/SuperEigen/src/PrePostProcessor.cpp.s

.PHONY : src/SuperEigen/src/PrePostProcessor.s

# target to generate assembly for a file
src/SuperEigen/src/PrePostProcessor.cpp.s:
	$(MAKE) -f CMakeFiles/run_sr_image.dir/build.make CMakeFiles/run_sr_image.dir/src/SuperEigen/src/PrePostProcessor.cpp.s
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/src/SuperEigen/src/PrePostProcessor.cpp.s
.PHONY : src/SuperEigen/src/PrePostProcessor.cpp.s

src/SuperEigen/src/SuperResConfig.o: src/SuperEigen/src/SuperResConfig.cpp.o

.PHONY : src/SuperEigen/src/SuperResConfig.o

# target to build an object file
src/SuperEigen/src/SuperResConfig.cpp.o:
	$(MAKE) -f CMakeFiles/run_sr_image.dir/build.make CMakeFiles/run_sr_image.dir/src/SuperEigen/src/SuperResConfig.cpp.o
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/src/SuperEigen/src/SuperResConfig.cpp.o
.PHONY : src/SuperEigen/src/SuperResConfig.cpp.o

src/SuperEigen/src/SuperResConfig.i: src/SuperEigen/src/SuperResConfig.cpp.i

.PHONY : src/SuperEigen/src/SuperResConfig.i

# target to preprocess a source file
src/SuperEigen/src/SuperResConfig.cpp.i:
	$(MAKE) -f CMakeFiles/run_sr_image.dir/build.make CMakeFiles/run_sr_image.dir/src/SuperEigen/src/SuperResConfig.cpp.i
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/src/SuperEigen/src/SuperResConfig.cpp.i
.PHONY : src/SuperEigen/src/SuperResConfig.cpp.i

src/SuperEigen/src/SuperResConfig.s: src/SuperEigen/src/SuperResConfig.cpp.s

.PHONY : src/SuperEigen/src/SuperResConfig.s

# target to generate assembly for a file
src/SuperEigen/src/SuperResConfig.cpp.s:
	$(MAKE) -f CMakeFiles/run_sr_image.dir/build.make CMakeFiles/run_sr_image.dir/src/SuperEigen/src/SuperResConfig.cpp.s
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/src/SuperEigen/src/SuperResConfig.cpp.s
.PHONY : src/SuperEigen/src/SuperResConfig.cpp.s

src/SuperEigen/src/SuperResEngine.o: src/SuperEigen/src/SuperResEngine.cpp.o

.PHONY : src/SuperEigen/src/SuperResEngine.o

# target to build an object file
src/SuperEigen/src/SuperResEngine.cpp.o:
	$(MAKE) -f CMakeFiles/run_sr_image.dir/build.make CMakeFiles/run_sr_image.dir/src/SuperEigen/src/SuperResEngine.cpp.o
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/src/SuperEigen/src/SuperResEngine.cpp.o
.PHONY : src/SuperEigen/src/SuperResEngine.cpp.o

src/SuperEigen/src/SuperResEngine.i: src/SuperEigen/src/SuperResEngine.cpp.i

.PHONY : src/SuperEigen/src/SuperResEngine.i

# target to preprocess a source file
src/SuperEigen/src/SuperResEngine.cpp.i:
	$(MAKE) -f CMakeFiles/run_sr_image.dir/build.make CMakeFiles/run_sr_image.dir/src/SuperEigen/src/SuperResEngine.cpp.i
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/src/SuperEigen/src/SuperResEngine.cpp.i
.PHONY : src/SuperEigen/src/SuperResEngine.cpp.i

src/SuperEigen/src/SuperResEngine.s: src/SuperEigen/src/SuperResEngine.cpp.s

.PHONY : src/SuperEigen/src/SuperResEngine.s

# target to generate assembly for a file
src/SuperEigen/src/SuperResEngine.cpp.s:
	$(MAKE) -f CMakeFiles/run_sr_image.dir/build.make CMakeFiles/run_sr_image.dir/src/SuperEigen/src/SuperResEngine.cpp.s
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/src/SuperEigen/src/SuperResEngine.cpp.s
.PHONY : src/SuperEigen/src/SuperResEngine.cpp.s

src/SyncVA/AVSyncManager.o: src/SyncVA/AVSyncManager.cpp.o

.PHONY : src/SyncVA/AVSyncManager.o

# target to build an object file
src/SyncVA/AVSyncManager.cpp.o:
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/src/SyncVA/AVSyncManager.cpp.o
.PHONY : src/SyncVA/AVSyncManager.cpp.o

src/SyncVA/AVSyncManager.i: src/SyncVA/AVSyncManager.cpp.i

.PHONY : src/SyncVA/AVSyncManager.i

# target to preprocess a source file
src/SyncVA/AVSyncManager.cpp.i:
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/src/SyncVA/AVSyncManager.cpp.i
.PHONY : src/SyncVA/AVSyncManager.cpp.i

src/SyncVA/AVSyncManager.s: src/SyncVA/AVSyncManager.cpp.s

.PHONY : src/SyncVA/AVSyncManager.s

# target to generate assembly for a file
src/SyncVA/AVSyncManager.cpp.s:
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/src/SyncVA/AVSyncManager.cpp.s
.PHONY : src/SyncVA/AVSyncManager.cpp.s

src/UI/FileListWidget.o: src/UI/FileListWidget.cpp.o

.PHONY : src/UI/FileListWidget.o

# target to build an object file
src/UI/FileListWidget.cpp.o:
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.o
.PHONY : src/UI/FileListWidget.cpp.o

src/UI/FileListWidget.i: src/UI/FileListWidget.cpp.i

.PHONY : src/UI/FileListWidget.i

# target to preprocess a source file
src/UI/FileListWidget.cpp.i:
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.i
.PHONY : src/UI/FileListWidget.cpp.i

src/UI/FileListWidget.s: src/UI/FileListWidget.cpp.s

.PHONY : src/UI/FileListWidget.s

# target to generate assembly for a file
src/UI/FileListWidget.cpp.s:
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/src/UI/FileListWidget.cpp.s
.PHONY : src/UI/FileListWidget.cpp.s

src/UI/ImagePreviewWidget.o: src/UI/ImagePreviewWidget.cpp.o

.PHONY : src/UI/ImagePreviewWidget.o

# target to build an object file
src/UI/ImagePreviewWidget.cpp.o:
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.o
.PHONY : src/UI/ImagePreviewWidget.cpp.o

src/UI/ImagePreviewWidget.i: src/UI/ImagePreviewWidget.cpp.i

.PHONY : src/UI/ImagePreviewWidget.i

# target to preprocess a source file
src/UI/ImagePreviewWidget.cpp.i:
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.i
.PHONY : src/UI/ImagePreviewWidget.cpp.i

src/UI/ImagePreviewWidget.s: src/UI/ImagePreviewWidget.cpp.s

.PHONY : src/UI/ImagePreviewWidget.s

# target to generate assembly for a file
src/UI/ImagePreviewWidget.cpp.s:
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImagePreviewWidget.cpp.s
.PHONY : src/UI/ImagePreviewWidget.cpp.s

src/UI/ImageProcessor.o: src/UI/ImageProcessor.cpp.o

.PHONY : src/UI/ImageProcessor.o

# target to build an object file
src/UI/ImageProcessor.cpp.o:
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.o
.PHONY : src/UI/ImageProcessor.cpp.o

src/UI/ImageProcessor.i: src/UI/ImageProcessor.cpp.i

.PHONY : src/UI/ImageProcessor.i

# target to preprocess a source file
src/UI/ImageProcessor.cpp.i:
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.i
.PHONY : src/UI/ImageProcessor.cpp.i

src/UI/ImageProcessor.s: src/UI/ImageProcessor.cpp.s

.PHONY : src/UI/ImageProcessor.s

# target to generate assembly for a file
src/UI/ImageProcessor.cpp.s:
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/src/UI/ImageProcessor.cpp.s
.PHONY : src/UI/ImageProcessor.cpp.s

src/UI/MainWindow.o: src/UI/MainWindow.cpp.o

.PHONY : src/UI/MainWindow.o

# target to build an object file
src/UI/MainWindow.cpp.o:
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.o
.PHONY : src/UI/MainWindow.cpp.o

src/UI/MainWindow.i: src/UI/MainWindow.cpp.i

.PHONY : src/UI/MainWindow.i

# target to preprocess a source file
src/UI/MainWindow.cpp.i:
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.i
.PHONY : src/UI/MainWindow.cpp.i

src/UI/MainWindow.s: src/UI/MainWindow.cpp.s

.PHONY : src/UI/MainWindow.s

# target to generate assembly for a file
src/UI/MainWindow.cpp.s:
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/src/UI/MainWindow.cpp.s
.PHONY : src/UI/MainWindow.cpp.s

src/UI/SettingsPanel.o: src/UI/SettingsPanel.cpp.o

.PHONY : src/UI/SettingsPanel.o

# target to build an object file
src/UI/SettingsPanel.cpp.o:
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.o
.PHONY : src/UI/SettingsPanel.cpp.o

src/UI/SettingsPanel.i: src/UI/SettingsPanel.cpp.i

.PHONY : src/UI/SettingsPanel.i

# target to preprocess a source file
src/UI/SettingsPanel.cpp.i:
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.i
.PHONY : src/UI/SettingsPanel.cpp.i

src/UI/SettingsPanel.s: src/UI/SettingsPanel.cpp.s

.PHONY : src/UI/SettingsPanel.s

# target to generate assembly for a file
src/UI/SettingsPanel.cpp.s:
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/src/UI/SettingsPanel.cpp.s
.PHONY : src/UI/SettingsPanel.cpp.s

src/Utils/FileUtils.o: src/Utils/FileUtils.cpp.o

.PHONY : src/Utils/FileUtils.o

# target to build an object file
src/Utils/FileUtils.cpp.o:
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/src/Utils/FileUtils.cpp.o
.PHONY : src/Utils/FileUtils.cpp.o

src/Utils/FileUtils.i: src/Utils/FileUtils.cpp.i

.PHONY : src/Utils/FileUtils.i

# target to preprocess a source file
src/Utils/FileUtils.cpp.i:
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/src/Utils/FileUtils.cpp.i
.PHONY : src/Utils/FileUtils.cpp.i

src/Utils/FileUtils.s: src/Utils/FileUtils.cpp.s

.PHONY : src/Utils/FileUtils.s

# target to generate assembly for a file
src/Utils/FileUtils.cpp.s:
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/src/Utils/FileUtils.cpp.s
.PHONY : src/Utils/FileUtils.cpp.s

src/Utils/LogUtils.o: src/Utils/LogUtils.cpp.o

.PHONY : src/Utils/LogUtils.o

# target to build an object file
src/Utils/LogUtils.cpp.o:
	$(MAKE) -f CMakeFiles/run_sr_image.dir/build.make CMakeFiles/run_sr_image.dir/src/Utils/LogUtils.cpp.o
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/src/Utils/LogUtils.cpp.o
.PHONY : src/Utils/LogUtils.cpp.o

src/Utils/LogUtils.i: src/Utils/LogUtils.cpp.i

.PHONY : src/Utils/LogUtils.i

# target to preprocess a source file
src/Utils/LogUtils.cpp.i:
	$(MAKE) -f CMakeFiles/run_sr_image.dir/build.make CMakeFiles/run_sr_image.dir/src/Utils/LogUtils.cpp.i
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/src/Utils/LogUtils.cpp.i
.PHONY : src/Utils/LogUtils.cpp.i

src/Utils/LogUtils.s: src/Utils/LogUtils.cpp.s

.PHONY : src/Utils/LogUtils.s

# target to generate assembly for a file
src/Utils/LogUtils.cpp.s:
	$(MAKE) -f CMakeFiles/run_sr_image.dir/build.make CMakeFiles/run_sr_image.dir/src/Utils/LogUtils.cpp.s
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/src/Utils/LogUtils.cpp.s
.PHONY : src/Utils/LogUtils.cpp.s

src/Utils/Logger.o: src/Utils/Logger.cpp.o

.PHONY : src/Utils/Logger.o

# target to build an object file
src/Utils/Logger.cpp.o:
	$(MAKE) -f CMakeFiles/run_sr_image.dir/build.make CMakeFiles/run_sr_image.dir/src/Utils/Logger.cpp.o
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/src/Utils/Logger.cpp.o
.PHONY : src/Utils/Logger.cpp.o

src/Utils/Logger.i: src/Utils/Logger.cpp.i

.PHONY : src/Utils/Logger.i

# target to preprocess a source file
src/Utils/Logger.cpp.i:
	$(MAKE) -f CMakeFiles/run_sr_image.dir/build.make CMakeFiles/run_sr_image.dir/src/Utils/Logger.cpp.i
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/src/Utils/Logger.cpp.i
.PHONY : src/Utils/Logger.cpp.i

src/Utils/Logger.s: src/Utils/Logger.cpp.s

.PHONY : src/Utils/Logger.s

# target to generate assembly for a file
src/Utils/Logger.cpp.s:
	$(MAKE) -f CMakeFiles/run_sr_image.dir/build.make CMakeFiles/run_sr_image.dir/src/Utils/Logger.cpp.s
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/src/Utils/Logger.cpp.s
.PHONY : src/Utils/Logger.cpp.s

src/WorkerPool/WorkerPool.o: src/WorkerPool/WorkerPool.cpp.o

.PHONY : src/WorkerPool/WorkerPool.o

# target to build an object file
src/WorkerPool/WorkerPool.cpp.o:
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.o
.PHONY : src/WorkerPool/WorkerPool.cpp.o

src/WorkerPool/WorkerPool.i: src/WorkerPool/WorkerPool.cpp.i

.PHONY : src/WorkerPool/WorkerPool.i

# target to preprocess a source file
src/WorkerPool/WorkerPool.cpp.i:
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.i
.PHONY : src/WorkerPool/WorkerPool.cpp.i

src/WorkerPool/WorkerPool.s: src/WorkerPool/WorkerPool.cpp.s

.PHONY : src/WorkerPool/WorkerPool.s

# target to generate assembly for a file
src/WorkerPool/WorkerPool.cpp.s:
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/src/WorkerPool/WorkerPool.cpp.s
.PHONY : src/WorkerPool/WorkerPool.cpp.s

src/main.o: src/main.cpp.o

.PHONY : src/main.o

# target to build an object file
src/main.cpp.o:
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.o
.PHONY : src/main.cpp.o

src/main.i: src/main.cpp.i

.PHONY : src/main.i

# target to preprocess a source file
src/main.cpp.i:
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.i
.PHONY : src/main.cpp.i

src/main.s: src/main.cpp.s

.PHONY : src/main.s

# target to generate assembly for a file
src/main.cpp.s:
	$(MAKE) -f CMakeFiles/VideoSRLiteGUI.dir/build.make CMakeFiles/VideoSRLiteGUI.dir/src/main.cpp.s
.PHONY : src/main.cpp.s

src/tools/run_sr_image.o: src/tools/run_sr_image.cpp.o

.PHONY : src/tools/run_sr_image.o

# target to build an object file
src/tools/run_sr_image.cpp.o:
	$(MAKE) -f CMakeFiles/run_sr_image.dir/build.make CMakeFiles/run_sr_image.dir/src/tools/run_sr_image.cpp.o
.PHONY : src/tools/run_sr_image.cpp.o

src/tools/run_sr_image.i: src/tools/run_sr_image.cpp.i

.PHONY : src/tools/run_sr_image.i

# target to preprocess a source file
src/tools/run_sr_image.cpp.i:
	$(MAKE) -f CMakeFiles/run_sr_image.dir/build.make CMakeFiles/run_sr_image.dir/src/tools/run_sr_image.cpp.i
.PHONY : src/tools/run_sr_image.cpp.i

src/tools/run_sr_image.s: src/tools/run_sr_image.cpp.s

.PHONY : src/tools/run_sr_image.s

# target to generate assembly for a file
src/tools/run_sr_image.cpp.s:
	$(MAKE) -f CMakeFiles/run_sr_image.dir/build.make CMakeFiles/run_sr_image.dir/src/tools/run_sr_image.cpp.s
.PHONY : src/tools/run_sr_image.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... install/strip"
	@echo "... install/local"
	@echo "... install"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... edit_cache"
	@echo "... run_sr_image"
	@echo "... VideoSRLiteGUI"
	@echo "... run_sr_image_autogen"
	@echo "... VideoSRLiteGUI_autogen"
	@echo "... VideoSRLiteGUI_autogen/mocs_compilation.o"
	@echo "... VideoSRLiteGUI_autogen/mocs_compilation.i"
	@echo "... VideoSRLiteGUI_autogen/mocs_compilation.s"
	@echo "... run_sr_image_autogen/mocs_compilation.o"
	@echo "... run_sr_image_autogen/mocs_compilation.i"
	@echo "... run_sr_image_autogen/mocs_compilation.s"
	@echo "... src/AppController/AppController.o"
	@echo "... src/AppController/AppController.i"
	@echo "... src/AppController/AppController.s"
	@echo "... src/AppController/WorkerPool.o"
	@echo "... src/AppController/WorkerPool.i"
	@echo "... src/AppController/WorkerPool.s"
	@echo "... src/AudioProc/AudioProc.o"
	@echo "... src/AudioProc/AudioProc.i"
	@echo "... src/AudioProc/AudioProc.s"
	@echo "... src/AudioProcessor/AudioProcessor.o"
	@echo "... src/AudioProcessor/AudioProcessor.i"
	@echo "... src/AudioProcessor/AudioProcessor.s"
	@echo "... src/Decoder/src/AudioDecoder.o"
	@echo "... src/Decoder/src/AudioDecoder.i"
	@echo "... src/Decoder/src/AudioDecoder.s"
	@echo "... src/Decoder/src/Decoder.o"
	@echo "... src/Decoder/src/Decoder.i"
	@echo "... src/Decoder/src/Decoder.s"
	@echo "... src/Decoder/src/VideoDecoder.o"
	@echo "... src/Decoder/src/VideoDecoder.i"
	@echo "... src/Decoder/src/VideoDecoder.s"
	@echo "... src/Encoder/AudioEncoder.o"
	@echo "... src/Encoder/AudioEncoder.i"
	@echo "... src/Encoder/AudioEncoder.s"
	@echo "... src/Encoder/Encoder.o"
	@echo "... src/Encoder/Encoder.i"
	@echo "... src/Encoder/Encoder.s"
	@echo "... src/Encoder/Muxer.o"
	@echo "... src/Encoder/Muxer.i"
	@echo "... src/Encoder/Muxer.s"
	@echo "... src/Encoder/VideoEncoder.o"
	@echo "... src/Encoder/VideoEncoder.i"
	@echo "... src/Encoder/VideoEncoder.s"
	@echo "... src/PostFilter/PostFilter.o"
	@echo "... src/PostFilter/PostFilter.i"
	@echo "... src/PostFilter/PostFilter.s"
	@echo "... src/PostFilter/PostFilterProcessor.o"
	@echo "... src/PostFilter/PostFilterProcessor.i"
	@echo "... src/PostFilter/PostFilterProcessor.s"
	@echo "... src/Processing/AudioDenoiser.o"
	@echo "... src/Processing/AudioDenoiser.i"
	@echo "... src/Processing/AudioDenoiser.s"
	@echo "... src/Processing/PostProcessor.o"
	@echo "... src/Processing/PostProcessor.i"
	@echo "... src/Processing/PostProcessor.s"
	@echo "... src/Processing/SuperResolution.o"
	@echo "... src/Processing/SuperResolution.i"
	@echo "... src/Processing/SuperResolution.s"
	@echo "... src/SuperEigen/src/ModelSession.o"
	@echo "... src/SuperEigen/src/ModelSession.i"
	@echo "... src/SuperEigen/src/ModelSession.s"
	@echo "... src/SuperEigen/src/PrePostProcessor.o"
	@echo "... src/SuperEigen/src/PrePostProcessor.i"
	@echo "... src/SuperEigen/src/PrePostProcessor.s"
	@echo "... src/SuperEigen/src/SuperResConfig.o"
	@echo "... src/SuperEigen/src/SuperResConfig.i"
	@echo "... src/SuperEigen/src/SuperResConfig.s"
	@echo "... src/SuperEigen/src/SuperResEngine.o"
	@echo "... src/SuperEigen/src/SuperResEngine.i"
	@echo "... src/SuperEigen/src/SuperResEngine.s"
	@echo "... src/SyncVA/AVSyncManager.o"
	@echo "... src/SyncVA/AVSyncManager.i"
	@echo "... src/SyncVA/AVSyncManager.s"
	@echo "... src/UI/FileListWidget.o"
	@echo "... src/UI/FileListWidget.i"
	@echo "... src/UI/FileListWidget.s"
	@echo "... src/UI/ImagePreviewWidget.o"
	@echo "... src/UI/ImagePreviewWidget.i"
	@echo "... src/UI/ImagePreviewWidget.s"
	@echo "... src/UI/ImageProcessor.o"
	@echo "... src/UI/ImageProcessor.i"
	@echo "... src/UI/ImageProcessor.s"
	@echo "... src/UI/MainWindow.o"
	@echo "... src/UI/MainWindow.i"
	@echo "... src/UI/MainWindow.s"
	@echo "... src/UI/SettingsPanel.o"
	@echo "... src/UI/SettingsPanel.i"
	@echo "... src/UI/SettingsPanel.s"
	@echo "... src/Utils/FileUtils.o"
	@echo "... src/Utils/FileUtils.i"
	@echo "... src/Utils/FileUtils.s"
	@echo "... src/Utils/LogUtils.o"
	@echo "... src/Utils/LogUtils.i"
	@echo "... src/Utils/LogUtils.s"
	@echo "... src/Utils/Logger.o"
	@echo "... src/Utils/Logger.i"
	@echo "... src/Utils/Logger.s"
	@echo "... src/WorkerPool/WorkerPool.o"
	@echo "... src/WorkerPool/WorkerPool.i"
	@echo "... src/WorkerPool/WorkerPool.s"
	@echo "... src/main.o"
	@echo "... src/main.i"
	@echo "... src/main.s"
	@echo "... src/tools/run_sr_image.o"
	@echo "... src/tools/run_sr_image.i"
	@echo "... src/tools/run_sr_image.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

